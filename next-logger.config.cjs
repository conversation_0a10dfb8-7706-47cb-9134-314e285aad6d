const pino = require('pino')

const prodConfig = {
  redact: {
    paths: ['password']
  }
}

const devConfig = {
  redact: {
    paths: ['password']
  },
  transport: {
    target: 'pino-pretty',
    options: {
      colorize: true,
      colorizeObjects: true,
      translateTime: 'HH:MM:ss',
      ignore: 'pid,hostname,name'
    }
  }
}

const logger = (defaultConfig) => {
  const config = process.env.NODE_ENV === 'development' ? devConfig : prodConfig
  return pino({ ...defaultConfig, ...config })
}

module.exports = { logger }
