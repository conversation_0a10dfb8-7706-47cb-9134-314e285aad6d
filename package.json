{"name": "swift", "type": "module", "scripts": {"build": "cross-env pnpm run lint && NODE_OPTIONS=--no-deprecation next build", "dev": "cross-env pnpm run lint && node startDev.js", "start-dev": "cross-env  NODE_OPTIONS=--no-deprecation next dev --port 4000 --experimental-https-ca \"$(mkcert -CAROOT)/rootCA.pem\" --experimental-https --experimental-https-key './nginx/certs/swift.local.key.pem' --experimental-https-cert './nginx/certs/swift.local.cert.pem'", "dev-safe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "generate:import-map": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint --fix --quiet && prettier --write --check .", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "start": "cross-env NODE_OPTIONS=--no-deprecation next start"}, "dependencies": {"@hey-api/client-fetch": "^0.4.2", "@hookform/resolvers": "^3.9.1", "@lexical/code": "0.21.0", "@lexical/history": "0.21.0", "@lexical/html": "0.21.0", "@lexical/link": "0.21.0", "@lexical/list": "0.21.0", "@lexical/markdown": "0.21.0", "@lexical/react": "0.21.0", "@lexical/rich-text": "0.21.0", "@lexical/utils": "0.21.0", "@payloadcms/db-mongodb": "3.19.0", "@payloadcms/email-resend": "3.19.0", "@payloadcms/next": "3.19.0", "@payloadcms/payload-cloud": "3.19.0", "@payloadcms/richtext-lexical": "3.19.0", "@payloadcms/storage-s3": "3.19.0", "@raddix/use-media-query": "^0.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@react-email/components": "0.0.31", "@react-email/render": "1.0.3", "@tanstack/react-query": "^5.59.20", "@tanstack/react-query-devtools": "^5.59.20", "body-scroll-lock": "4.0.0-beta.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cross-env": "^7.0.3", "install": "^0.13.0", "jose": "^5.9.6", "lexical": "0.21.0", "lodash": "^4.17.21", "lucide-react": "^0.461.0", "motion": "^12.5.0", "nanoid": "^5.1.5", "next": "15.0.0", "next-intl": "^3.26.3", "next-logger": "^5.0.1", "next-themes": "^0.4.4", "pagedjs": "^0.4.3", "payload": "3.19.0", "peek-readable": "5.3.1", "pino": "^9.5.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "7.57.0", "react-hotkeys-hook": "^4.6.1", "resend": "^4.0.1", "sharp": "^0.33.5", "sonner": "^1.7.4", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "tiny-cookie": "^2.5.1", "validator": "^13.12.0", "vaul": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"@hey-api/openapi-ts": "^0.54.1", "@tanstack/eslint-plugin-query": "^5.60.1", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/node": "^22.5.4", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1", "autoprefixer": "^10.4.20", "camelcase": "^8.0.0", "eslint": "^8", "eslint-config-next": "15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-disable": "^2.0.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-unicorn": "^56.0.1", "husky": "^9.1.6", "lint-staged": "^15.2.10", "pino-pretty": "^13.0.0", "postcss": "^8.4.49", "semver": "^7.6.3", "tailwindcss": "^3.4.15", "typescript": "5.6.3"}, "engines": {"node": "^22"}, "pnpm": {"overrides": {"peek-readable": "$peek-readable"}}, "packageManager": "pnpm@9.15.0"}