import { useCallback } from 'react'
import { useResume } from '@hooks'

export const useFieldAdd = ({ append, sectionKey, data, newItem }) => {
  const { updateResume } = useResume()
  const handleAdd = useCallback(() => {
    const updatedArray = [...data, newItem]
    append(newItem)
    updateResume({ [sectionKey]: updatedArray })
  }, [append, sectionKey, data, updateResume, newItem])
  return handleAdd
}
