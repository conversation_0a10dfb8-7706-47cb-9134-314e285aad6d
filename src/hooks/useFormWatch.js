import { useEffect } from 'react'
import { useResume } from '@hooks'
import { resumeValues } from '@utils'

export const useFormWatch = (watch, key, trigger) => {
  const { updateResume } = useResume()

  useEffect(() => {
    const subscription = watch(async (data, { name, type }) => {
      if (name && type === 'change') {
        if (trigger) {
          const valid = await trigger()
          if (!valid) return
        }
        const cleanFunction = resumeValues[key]
        const cleanedData = cleanFunction(data, key)
        updateResume(cleanedData)
      }
    })

    return () => subscription.unsubscribe()
  }, [watch, updateResume, key, trigger])
}
