import { useEffect } from 'react'
import { animate, useMotionValue } from 'motion/react'

const inactiveShadow = '0px 0px 0px rgba(0,0,0,0.8)'

export const useRaisedShadow = (value) => {
  const boxShadow = useMotionValue(inactiveShadow)

  useEffect(() => {
    let isActive = false
    value.on('change', (latest) => {
      const wasActive = isActive
      if (latest === 0) {
        isActive = false
        if (isActive !== wasActive) {
          animate(boxShadow, inactiveShadow)
        }
      } else {
        isActive = true
        if (isActive !== wasActive) {
          animate(boxShadow, '5px 5px 10px rgba(0,0,0,0.3)')
        }
      }
    })
  }, [value, boxShadow])

  return boxShadow
}
