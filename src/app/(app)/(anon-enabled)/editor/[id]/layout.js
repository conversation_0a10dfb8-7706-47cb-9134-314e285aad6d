import Image from 'next/image'
import { AuthHeader } from '@components'
import { auth } from '@constants'
import { AuthProvider, KeyboardShortcutsEditorProvider, ResumeProvider } from '@context'
import { getResumeById } from '@data'
import { HydrationBoundary, QueryClient, dehydrate } from '@tanstack/react-query'

export default async function EditorLayout({ children, params }) {
  const { id } = await params
  const queryClient = new QueryClient()

  await queryClient.prefetchQuery({
    queryKey: ['getResume', id],
    queryFn: () => getResumeById(id)
  })

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <AuthProvider authType={auth.types.anonEnabled}>
        <ResumeProvider id={id}>
          <AuthHeader />
          <KeyboardShortcutsEditorProvider />
          <main className='relative flex-auto flex overflow-hidden'>
            <Image
              src='/images/canvas-bg.svg'
              alt='canvas background'
              width={1000}
              height={1000}
              className='absolute inset-0 w-full h-full object-cover -z-10'
            />
            {children}
          </main>
        </ResumeProvider>
      </AuthProvider>
    </HydrationBoundary>
  )
}
