import Image from 'next/image'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FontPreloader } from '@components'
import { auth } from '@constants'
import { AuthProvider, KeyboardShortcutsEditorProvider, ResumeProvider } from '@context'
import { getResumeById } from '@data'
import { HydrationBoundary, QueryClient, dehydrate } from '@tanstack/react-query'

export default async function EditorLayout({ children, params }) {
  const { id } = await params
  const queryClient = new QueryClient()

  // Prefetch resume data for font preloading
  const resumeData = await getResumeById(id)

  await queryClient.prefetchQuery({
    queryKey: ['getResume', id],
    queryFn: () => getResumeById(id)
  })

  // Extract selected font for preloading
  const selectedFont = resumeData?.design?.font

  return (
    <>
      {/* Preload selected font */}
      <FontPreloader selectedFont={selectedFont} />

      <HydrationBoundary state={dehydrate(queryClient)}>
        <AuthProvider authType={auth.types.anonEnabled}>
          <ResumeProvider id={id}>
            <AuthHeader />
            <KeyboardShortcutsEditorProvider />
            <main className='relative flex-auto flex overflow-hidden'>
              <Image
                src='/images/canvas-bg.svg'
                alt='canvas background'
                width={1000}
                height={1000}
                className='absolute inset-0 w-full h-full object-cover -z-10'
              />
              {children}
            </main>
          </ResumeProvider>
        </AuthProvider>
      </HydrationBoundary>
    </>
  )
}
