import { Suspense } from 'react'
import {
  AllResumes,
  ErrorBoundary,
  RecentResumes,
  ResumeGridSkelton,
  TopBar
} from '@components'

export default async function Dashboard() {
  return (
    <>
      <TopBar />
      <div className='flex-1 flex flex-col overflow-y-auto'>
        <ErrorBoundary>
          <Suspense fallback={<ResumeGridSkelton items={4} />}>
            <RecentResumes />
          </Suspense>
        </ErrorBoundary>
        <ErrorBoundary>
          <Suspense fallback={<ResumeGridSkelton items={4} />}>
            <AllResumes />
          </Suspense>
        </ErrorBoundary>
      </div>
    </>
  )
}
