import { getTranslations } from 'next-intl/server'
import Link from 'next/link'
import { SeparatorWithText, SocialLoginForm, Text } from '@components'
import { auth, routes } from '@constants'
import { SignUpForm } from './SignUpForm'

export async function generateMetadata() {
  const t = await getTranslations('SignUpPage')
  return {
    title: t('metaTitle'),
    description: t('metaDescription')
  }
}

export default async function Page() {
  const t = await getTranslations('SignUpPage')
  const tc = await getTranslations('Common')

  return (
    <>
      <div>
        <Text as='h1' variant='3xl' className='text-slate-700 mb-2.5' weight='semibold'>
          {t('title')}
        </Text>
        <Text variant='sm' weight='medium' className='text-slate-700'>
          {t('haveAccount')}{' '}
          <Link className='text-primary capitalize' href={routes.signIn}>
            {tc('signInLink')}
          </Link>
        </Text>
      </div>
      <div className='relative'>
        <SocialLoginForm
          provider={auth.social.providers.LINKEDIN}
          label={tc('signUpWithLinkedIn')}
        />
        <SeparatorWithText className='my-6' text={tc('orUseEmail')} />
        <SignUpForm />
      </div>
    </>
  )
}
