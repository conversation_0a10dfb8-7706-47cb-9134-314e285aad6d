'use client'

import { useTransition } from 'react'
import { Check, ChevronDown } from 'lucide-react'
import { useLocale, useTranslations } from 'next-intl'
import { setUserLocale } from '@/actions'
import { locales } from '@/i18n/config'
import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@components'

export const LocaleSwitcher = () => {
  const [isPending, startTransition] = useTransition()
  const t = useTranslations('LocaleSwitcher')
  const locale = useLocale()

  const handleLocaleChange = async (newLocale) => {
    startTransition(() => {
      setUserLocale(newLocale)
    })
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='secondary'
          size='sm'
          disabled={isPending}
          className='uppercase gap-1'>
          {locale}
          <ChevronDown className='h-3 w-3 opacity-50' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='start'>
        {locales.map((loc) => (
          <DropdownMenuItem
            key={loc}
            onClick={() => handleLocaleChange(loc)}
            className='cursor-pointer'>
            <Check
              className={`mr-2 h-4 w-4 ${locale === loc ? 'opacity-100' : 'opacity-0'}`}
            />
            {t(loc)}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
