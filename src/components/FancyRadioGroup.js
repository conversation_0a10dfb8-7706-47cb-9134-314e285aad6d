'use client'

import { useTranslations } from 'next-intl'
import {
  FormControl,
  FormItem,
  FormLabel,
  RadioGroup,
  RadioGroupItem,
  Text
} from '@components'
import { cn } from '@utils'

export const FancyRadioGroup = ({ name, optionsData, value, onChange }) => {
  const tc = useTranslations('Common')
  const { options, icons } = optionsData
  return (
    <RadioGroup
      value={value}
      onValueChange={onChange}
      className='p-1 border border-slate-200 rounded bg-white inline-flex gap-1'>
      {options.map((option, index) => {
        const Icon = icons ? icons?.[option?.value] : <></>
        return (
          <FormItem className='space-y-0' key={`${name}-${index}`}>
            <FormControl className='sr-only'>
              <RadioGroupItem value={option.value} />
            </FormControl>
            <FormLabel
              className={cn(
                'w-auto h-full text-center cursor-pointer border border-transparent px-3 py-2 rounded flex flex-col items-center gap-2 transition-all hover:bg-gray-50 hover:border-slate-200 p-1.5 lg:px-3 lg:py-2 lg:w-[99px]',
                value === option.value && 'bg-gray-50 border-slate-200'
              )}>
              <Icon />
              <Text variant='xs' weight='medium' className='text-slate-600'>
                {tc(option?.value)}
              </Text>
            </FormLabel>
          </FormItem>
        )
      })}
    </RadioGroup>
  )
}
