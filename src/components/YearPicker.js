import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue
} from '@components'
import { cn } from '@utils'

const generateYearRange = (startYear, endYear) => {
  const years = []
  for (let year = startYear; year <= endYear; year++) {
    years.push({
      value: year.toString(),
      label: year.toString()
    })
  }
  return years.reverse()
}

export const YearPicker = ({
  className,
  triggerClass,
  placeholder,
  label,
  onChange,
  disabled,
  ...props
}) => {
  const currentYear = new Date().getFullYear()
  const years = generateYearRange(1966, currentYear)
  return (
    <Select
      onValueChange={onChange}
      className={cn(className)}
      disabled={disabled}
      {...props}>
      <SelectTrigger className={cn(triggerClass)}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel className='sr-only'>{label}</SelectLabel>
          {years.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  )
}
