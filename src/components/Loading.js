'use client'

import { useEffect, useRef } from 'react'
import { disableBodyScroll, enableBodyScroll } from 'body-scroll-lock'
import { LoadingSpinner } from '@components/ui'

export const Loading = () => {
  const overlayRef = useRef(null)

  useEffect(() => {
    const overlay = overlayRef.current

    if (overlay) {
      disableBodyScroll(overlay)
    }

    return () => {
      if (overlay) {
        enableBodyScroll(overlay)
      }
    }
  }, [])

  return (
    <div>
      <div
        ref={overlayRef}
        className='fixed inset-0 flex items-center justify-center bg-white bg-opacity-25 z-1'>
        <LoadingSpinner size='xl' />
      </div>
    </div>
  )
}
