import {
  AuthBreadcrumb,
  LocaleSwitcher,
  Separator,
  SwiftLogo,
  Text,
  UserNavMenu
} from '@components'

export const AuthHeader = () => {
  return (
    <header className='flex-shrink-0 flex flex-col gap-6 px-3 py-2 border-b border-slate-200 dark:border-slate-800'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-3.5'>
          <div>
            <SwiftLogo className='w-auto h-6' />
            <Text className='sr-only'>Swift Resume Builder</Text>
          </div>
          <Separator orientation='vertical' className='h-4 hidden md:block' />
          <AuthBreadcrumb />
        </div>
        <div className='flex items-center gap-3.5'>
          <LocaleSwitcher />
          <UserNavMenu />
        </div>
      </div>
    </header>
  )
}
