import Image from 'next/image'
import { SwiftLogo } from '@components'

export const AuthLayout = ({ children }) => {
  return (
    <div className='grid min-h-svh lg:grid-cols-2 p-4 md:p-7'>
      <div className='flex flex-col gap-4'>
        <div className='flex flex-1 items-center justify-center'>
          <div className='w-full max-w-[336px] space-y-8'>
            <SwiftLogo />
            {children}
          </div>
        </div>
      </div>
      <div className='relative hidden bg-muted lg:block'>
        <Image
          src='/images/swift-login.jpg'
          alt='Swift'
          width={622}
          height={926}
          className='absolute inset-0 h-full w-full object-cover rounded-3xl border-2 border-slate-100'
        />
      </div>
    </div>
  )
}
