'use client'

import { Loader2, Plus } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { Button } from '@components'
import { useResumes } from '@hooks'

export const CreateResumeButton = () => {
  const tc = useTranslations('Common')
  const { createResume, isCreatingResume } = useResumes()

  return (
    <Button onClick={createResume} isLoading={isCreatingResume}>
      {isCreatingResume ? (
        <Loader2 className='animate-spin w-4 h-4' />
      ) : (
        <Plus className='w-4 h-4' />
      )}
      {tc('createResume')}
    </Button>
  )
}
