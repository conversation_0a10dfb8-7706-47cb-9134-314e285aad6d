'use client'

import { useRef, useState } from 'react'
import { useTranslations } from 'next-intl'
import { Button, SparkelIcon, Textarea } from '@components'

export const EditableTextArea = ({ value, onChange }) => {
  const [content, setContent] = useState(value)
  const textareaRef = useRef(null)
  const tc = useTranslations('Common')

  const handleChange = (event) => {
    let newContent = event.target.value

    // If content is empty or just a bullet point, clear it
    if (newContent === '' || newContent === '•' || newContent === '• ') {
      setContent('')
      return
    }

    // If content is empty or doesn't start with bullet point, add it
    if (!newContent.startsWith('• ')) {
      newContent = '• ' + newContent
    }

    // Handle multiple lines
    const lines = newContent.split('\n')
    const formattedLines = lines.map((line) => {
      const trimmedLine = line.trim()
      if (trimmedLine === '') return ''
      if (trimmedLine.startsWith('•')) return line
      return `• ${trimmedLine}`
    })

    setContent(formattedLines.join('\n'))
    onChange(formattedLines.join('\n'))
  }

  const handleKeyDown = (event) => {
    if (event.key === 'Enter') {
      event.preventDefault()
      const textarea = textareaRef.current
      if (textarea) {
        const { selectionStart, selectionEnd, value } = textarea
        const beforeCursor = value.slice(0, selectionStart)
        const afterCursor = value.slice(selectionEnd)

        const newContent =
          beforeCursor.trim() === ''
            ? `• ${afterCursor}`
            : `${beforeCursor}\n• ${afterCursor}`

        setContent(newContent)

        // Set cursor position after the new bullet point
        setTimeout(() => {
          const newCursorPosition = beforeCursor.length + 3
          textarea.setSelectionRange(newCursorPosition, newCursorPosition)
        }, 0)
      }
    }
  }

  return (
    <div className='relative'>
      <Textarea
        className='p-4 pb-16 bg-white group text-sm font-normal text-slate-700 resize-none'
        ref={textareaRef}
        value={content}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        spellCheck='true'
        rows={10}
      />
      <div className='w-auto bg-white pt-3 absolute bottom-px left-4 right-4 group-disabled:bg-slate-50'>
        <div className='border-t border-slate-200 pt-3 pb-4'>
          <Button type='button' variant='secondary' size='sm'>
            {tc('suggestions')} <SparkelIcon className='fill-yellow-400' />
          </Button>
        </div>
      </div>
    </div>
  )
}
