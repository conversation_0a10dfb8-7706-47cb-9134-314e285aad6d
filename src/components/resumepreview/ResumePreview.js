'use client'

import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import { FontPreloader } from '@components'
import { pageSizes } from '@constants'
import { renderComponentToHTML, renderPagesWithSmartBreaks } from '@handlers/chunker'
import { useResume } from '@hooks'
import { debounce, resumeHasSidebar, resumeOptionValue } from '@utils'
import { Resume } from './Resume'
import { ResumeMain } from './ResumeMain'
import { ResumeResizableContainer } from './ResumeResizableContainer'
import { ResumeSidebar } from './ResumeSidebar'

const { A4 } = pageSizes

export const ResumePreview = () => {
  const { resume } = useResume()
  const [resumePages, setResumePages] = useState({ main: [], sidebar: [] })
  const tc = useTranslations('Common')
  const mt = useTranslations('Months')

  const generatePages = useCallback(async () => {
    const mainHTML = renderComponentToHTML(
      <ResumeMain resumeData={resume} tc={tc} mt={mt} />
    )

    const hasSidebar = resumeHasSidebar(resume.design.layout)
    const width = hasSidebar ? A4.width * 0.67 : A4.width

    const mainResult = await renderPagesWithSmartBreaks(mainHTML, [], width, A4.height, {
      offset: 30
    })

    const sidebarHTML = renderComponentToHTML(
      <ResumeSidebar resumeData={resume} tc={tc} mt={mt} />
    )
    const sideBarOffset = resume.design.sidebarStyle === resumeOptionValue.inset ? 40 : 30
    const sidebarResult = await renderPagesWithSmartBreaks(
      sidebarHTML,
      [],
      A4.width * 0.33,
      A4.height,
      {
        offset: sideBarOffset
      }
    )
    setResumePages({
      main: mainResult?.pages || [],
      sidebar: sidebarResult?.pages || []
    })
  }, [resume, tc, mt])

  const debouncedGeneratePages = useMemo(() => {
    const debouncedFunction = debounce(generatePages, 50)

    return {
      execute: debouncedFunction,
      cancel: () => debouncedFunction.cancel(),
      flush: () => debouncedFunction.flush()
    }
  }, [generatePages])

  useEffect(() => {
    debouncedGeneratePages.execute()

    return () => {
      debouncedGeneratePages.cancel()
    }
  }, [resume, tc, debouncedGeneratePages])

  const totalPages = Math.max(resumePages.main.length, resumePages.sidebar.length, 1)

  return (
    <>
      {/* Preload selected font */}
      <FontPreloader selectedFont={resume?.design?.font} />

      <ResumeResizableContainer>
        {Array.from({ length: totalPages }, (_, pageIndex) => {
          const mainPage = resumePages.main[pageIndex]
          const sidebarPage = resumePages.sidebar[pageIndex]

          return (
            <Resume
              key={'page-' + pageIndex}
              pageIndex={pageIndex + 1}
              totalPages={totalPages}
              resumeData={resume}
              mainContent={mainPage?.contentOnly}
              sidebarContent={sidebarPage?.contentOnly}
            />
          )
        })}
      </ResumeResizableContainer>
    </>
  )
}
