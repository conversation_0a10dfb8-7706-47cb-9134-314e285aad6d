import { convertLexicalToHtml, months, resumeOptionValue } from '@utils'
import {
  MonthYear,
  RichText,
  SectionContainer,
  SectionItem,
  SectionItems,
  SectionTitle,
  SectionTop,
  SubTitle,
  Title
} from './ui'

export const Experience = ({ resumeData, tc, location, mt }) => {
  const {
    experience,
    design: { layout, palette, headingUnderlineStyle, sections }
  } = resumeData

  const sectionData =
    sections.find((section) => section.sectionKey === resumeOptionValue?.experience) || {}

  const sectionTitle =
    resumeOptionValue?.experience === sectionData?.sectionName
      ? tc(resumeOptionValue.experience)
      : sectionData?.sectionName

  const visibleExperiences = experience.filter((item) => item.isVisible)

  if (!visibleExperiences?.length) return null

  return (
    <SectionContainer layout={layout}>
      <SectionTitle
        title={sectionTitle}
        location={location}
        layout={layout}
        palette={palette}
        headingUnderlineStyle={headingUnderlineStyle}
      />
      <SectionItems>
        {visibleExperiences.map((item, index) => {
          const {
            title,
            subTitle,
            startMonth,
            startYear,
            endMonth,
            endYear,
            description,
            isPresent
          } = item

          const startMonthLabel = months.find(
            (month) => month.value === startMonth
          )?.label
          const endMonthLabel = months.find((month) => month.value === endMonth)?.label

          const startDate = startMonthLabel
            ? `${mt(startMonthLabel)} ${startYear}`
            : startYear
          const endDate = endMonthLabel ? `${mt(endMonthLabel)} ${endYear}` : endYear

          const htmlContent = convertLexicalToHtml(description)

          return (
            <SectionItem key={`experience-${index}`}>
              <SectionTop>
                {title && (
                  <Title palette={palette} location={location}>
                    {title}
                  </Title>
                )}
                {subTitle && (
                  <SubTitle palette={palette} location={location}>
                    {subTitle}
                  </SubTitle>
                )}
                {startMonth && (endMonth || isPresent) && (
                  <MonthYear palette={palette} location={location}>
                    {startDate}{' '}
                    {isPresent ? `- ${tc('present')}` : endDate ? `- ${endDate}` : ''}
                  </MonthYear>
                )}
              </SectionTop>
              {htmlContent && (
                <RichText
                  palette={palette}
                  location={location}
                  htmlContent={htmlContent}
                />
              )}
            </SectionItem>
          )
        })}
      </SectionItems>
    </SectionContainer>
  )
}
