import {
  ContactContainer,
  ContactItem,
  PersonalInfoDetail,
  PersonalInfoTitle
} from './ui'

export const ContactDetails = ({ resumeData, tc, location }) => {
  const { design, phone, email, links } = resumeData
  const { layout, palette, accent } = design

  return (
    <ContactContainer location={location}>
      {email && (
        <ContactItem layout={layout}>
          <PersonalInfoTitle
            palette={palette}
            location={location}
            accent={accent}
            layout={layout}>
            {tc('email')}:
          </PersonalInfoTitle>
          <PersonalInfoDetail
            palette={palette}
            location={location}
            accent={accent}
            layout={layout}>
            {email}
          </PersonalInfoDetail>
        </ContactItem>
      )}
      {phone && (
        <ContactItem layout={layout}>
          <PersonalInfoTitle
            palette={palette}
            location={location}
            accent={accent}
            layout={layout}>
            {tc('phone')}:
          </PersonalInfoTitle>
          <PersonalInfoDetail
            palette={palette}
            location={location}
            accent={accent}
            layout={layout}>
            {phone}
          </PersonalInfoDetail>
        </ContactItem>
      )}
      {links?.length > 0 &&
        links.map((link, index) => (
          <ContactItem key={`link-${index}`} layout={layout}>
            {link.name && (
              <PersonalInfoTitle
                palette={palette}
                location={location}
                accent={accent}
                layout={layout}>
                {link.name}:
              </PersonalInfoTitle>
            )}
            {link.url && (
              <PersonalInfoDetail
                palette={palette}
                location={location}
                accent={accent}
                layout={layout}>
                {link.url}
              </PersonalInfoDetail>
            )}
          </ContactItem>
        ))}
    </ContactContainer>
  )
}
