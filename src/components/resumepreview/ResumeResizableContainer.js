'use client'

import React, { useRef } from 'react'
import { pageSizes } from '@constants'
import { useResizeObserver } from '@hooks'

const { A4, MAX_WIDTH, TARGET_RATIO } = pageSizes

export function ResumeResizableContainer({ children }) {
  const containerRef = useRef(null)
  const { width: containerWidth } = useResizeObserver({
    ref: containerRef,
    box: 'border-box'
  })

  const baseScale = containerWidth ? Math.min(containerWidth / A4.width, 1) : 1
  const targetWidth = containerWidth ? containerWidth * TARGET_RATIO : 0
  let zoomFactor = targetWidth && baseScale ? targetWidth / (A4.width * baseScale) : 1
  const maxZoom = MAX_WIDTH / (A4.width * baseScale)
  zoomFactor = Math.min(Math.max(zoomFactor, 1), maxZoom)
  const finalScale = baseScale * zoomFactor
  const childCount = React.Children.count(children)
  const height =
    childCount > 0
      ? A4.height * finalScale * childCount + 20 * finalScale * (childCount - 1)
      : 0

  return (
    <div ref={containerRef} className='relative flex justify-center items-start w-full'>
      <div
        style={{
          width: '100%',
          height,
          pointerEvents: 'none'
        }}></div>
      <div
        id='resume-preview-container'
        className='space-y-5'
        style={{
          transform: `scale(${finalScale})`,
          transformOrigin: 'top center',
          width: A4.width,
          position: 'absolute',
          top: 0
        }}>
        {children}
      </div>
    </div>
  )
}
