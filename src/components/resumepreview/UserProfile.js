import { resumeHasSidebar } from '@utils'
import { JobTitle, UserName, UserPicture, UserProfileContainer } from './ui'

export const UserProfile = ({ resumeData, location }) => {
  const { firstName, lastName, jobTitle, profileImage, showProfileImage, design } =
    resumeData
  const { layout, palette, accent, avatarStyle } = design

  const hasSidebar = resumeHasSidebar(layout)

  return (
    <UserProfileContainer hasSidebar={hasSidebar}>
      {showProfileImage && profileImage && (
        <UserPicture profileImage={profileImage} avatarStyle={avatarStyle} />
      )}
      <div>
        <UserName
          firstName={firstName}
          lastName={lastName}
          palette={palette}
          location={location}
          accent={accent}
          hasSidebar={hasSidebar}
        />
        {jobTitle && (
          <JobTitle
            jobTitle={jobTitle}
            palette={palette}
            accent={accent}
            location={location}
            hasSidebar={hasSidebar}
          />
        )}
      </div>
    </UserProfileContainer>
  )
}
