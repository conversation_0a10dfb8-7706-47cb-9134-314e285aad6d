'use client'

import { resumeHasSidebar, resumeOptionValue } from '@utils'
import { AdditionalProficiencies } from './AdditionalProficiencies'
import { ContactDetails } from './ContactDetails'
import { CustomSections } from './CustomSections'
import { Education } from './Education'
import { Experience } from './Experience'
import { Skills } from './Skills'
import { SectionsContainer, SidebarInnerContainer, SidebarLayout } from './ui'

const sectionComponents = {
  [resumeOptionValue.skills]: Skills,
  [resumeOptionValue.experience]: Experience,
  [resumeOptionValue.education]: Education,
  [resumeOptionValue.proficiencies]: AdditionalProficiencies,
  [resumeOptionValue.customSections]: CustomSections
}

export const ResumeSidebar = ({ resumeData, tc = {}, mt = {} }) => {
  const { design } = resumeData
  const { layout, sections, sidebarStyle, palette, spacing } = design

  const hasSidebar = resumeHasSidebar(layout)

  if (!hasSidebar || !sections?.length) {
    return <div></div>
  }

  const sidebarSections = sections.filter(
    (section) => section.isVisible && section.showInSidebar
  )

  return (
    <SidebarLayout sidebarStyle={sidebarStyle}>
      <SidebarInnerContainer sidebarStyle={sidebarStyle} palette={palette}>
        <SectionsContainer spacing={spacing}>
          <ContactDetails resumeData={resumeData} tc={tc} location='sidebar' />
          {sidebarSections.map((section, index) => {
            const Component = sectionComponents[section.sectionKey]
            let counter = 500
            return Component ? (
              <Component
                key={section.id}
                resumeData={resumeData}
                tc={tc}
                location='sidebar'
                mt={mt}
                counter={counter + index}
              />
            ) : null
          })}
        </SectionsContainer>
      </SidebarInnerContainer>
    </SidebarLayout>
  )
}
