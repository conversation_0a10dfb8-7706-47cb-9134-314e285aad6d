import { resumeStyles } from '@utils'

export const UserPicture = ({ profileImage, avatarStyle, ...props }) => {
  const containerStyle = resumeStyles.userPictureContainer()
  const imgStyle = resumeStyles.userPicture(avatarStyle)

  const thumbnail = profileImage?.thumbnailURL || profileImage?.url || null

  if (!thumbnail) return null
  return (
    <div style={containerStyle}>
      {/*eslint-disable-next-line @next/next/no-img-element */}
      <img
        {...props}
        src={thumbnail}
        alt='User'
        width={70}
        height={70}
        style={imgStyle}
        loading='lazy'
      />
    </div>
  )
}
