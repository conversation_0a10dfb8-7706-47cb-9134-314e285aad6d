import { resumeOptionValue, resumeOptions, resumeStyles } from '@utils'

export const ProfileHeader = ({ children, accent, hasSidebar }) => {
  const profileHeaderStyle = resumeStyles.profileHeader(accent, hasSidebar)
  const accentStyles = resumeOptions.accent.styles[accent]

  const imgSource =
    accent === resumeOptionValue.header && !hasSidebar
      ? `/images/${accentStyles.backgroundImage}`
      : null

  return (
    <div style={profileHeaderStyle}>
      <div
        style={{
          position: 'absolute',
          inset: 0,
          width: '100%',
          height: '100%'
        }}>
        {imgSource && (
          <img
            src={imgSource}
            width={535}
            height={200}
            alt='header background'
            fetchPriority='low'
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover'
            }}
          />
        )}
      </div>
      {children}
    </div>
  )
}
