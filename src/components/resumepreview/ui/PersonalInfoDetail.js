import { resumeHasSidebar, resumeStyles } from '@utils'

export const PersonalInfoDetail = ({
  children,
  isLink = false,
  href,
  palette,
  location,
  accent,
  layout,
  ...props
}) => {
  const hasSidebar = resumeHasSidebar(layout)

  const personalInfoDetailStyle = resumeStyles.personalInfoDetail(
    hasSidebar,
    palette,
    location,
    accent
  )

  if (isLink) {
    return (
      <a style={personalInfoDetailStyle} href={href} {...props}>
        {children}
      </a>
    )
  }
  return (
    <div style={personalInfoDetailStyle} {...props}>
      {children}
    </div>
  )
}
