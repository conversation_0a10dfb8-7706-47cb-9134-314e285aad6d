import React from 'react'
import { resumeStyles } from '@utils'

export const SkillsList = ({ children, columns = 4, location }) => {
  const childrenArray = React.Children.toArray(children)
  const itemCount = childrenArray?.length || 0
  const skillsListStyle = resumeStyles.skillsList()
  const singleColumnStyle = resumeStyles.skillListColumn()
  const skillListRowStyle = resumeStyles.skillListRow()

  if (location === 'sidebar') {
    return <div style={singleColumnStyle}>{childrenArray}</div>
  }

  const itemsPerRow = columns // 4 items per row
  const totalRows = Math.ceil(itemCount / itemsPerRow)

  const organizedRows = []

  for (let rowIndex = 0; rowIndex < totalRows; rowIndex++) {
    const rowItems = []

    for (let columnIndex = 0; columnIndex < itemsPerRow; columnIndex++) {
      const itemIndex = rowIndex * itemsPerRow + columnIndex
      if (itemIndex < itemCount) {
        rowItems.push(childrenArray[itemIndex])
      }
    }

    if (rowItems.length > 0) {
      organizedRows.push(
        <div key={`row-${rowIndex}`} style={skillListRowStyle}>
          {rowItems}
        </div>
      )
    }
  }

  return <div style={skillsListStyle}>{organizedRows}</div>
}
