import { nanoid } from 'nanoid/non-secure'
import { resumeStyles } from '@utils'

export const SectionTitle = ({
  title,
  layout,
  palette,
  headingUnderlineStyle,
  location,
  ...props
}) => {
  const sectionTitleStyle = resumeStyles.sectionTitle(layout)

  if (!title) {
    return <div className='section-heading' style={sectionTitleStyle}></div>
  }

  const sectionTitleSpanStyle = resumeStyles.sectionTitleSpan(
    layout,
    palette,
    location,
    headingUnderlineStyle
  )
  const id = nanoid()
  return (
    <div
      className={`section-heading smart-break-target smart-section-${id}`}
      style={sectionTitleStyle}
      {...props}>
      <span style={sectionTitleSpanStyle}>{title}</span>
    </div>
  )
}
