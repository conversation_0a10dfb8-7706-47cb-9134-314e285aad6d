import { resumeHasSidebar, resumeStyles } from '@utils'
import { ResumePage } from './ResumePage'
import { PageNumber, ResumeLayout, SidebarInnerContainer, SidebarLayout } from './ui'

export const Resume = ({
  pageIndex,
  totalPages,
  resumeData,
  mainContent,
  sidebarContent
}) => {
  const { design } = resumeData
  const { layout, sections, palette, sidebarStyle, pageNumbers } = design
  const hasSidebar = resumeHasSidebar(layout)

  if (!sections?.length) return null

  const mainOuterStyle = resumeStyles.mainOuter()
  const sidebarOuterStyle = resumeStyles.sidebarOuter()

  return (
    <ResumePage resumeData={resumeData}>
      <ResumeLayout hasSidebar={hasSidebar} layout={layout}>
        <div
          style={mainOuterStyle}
          dangerouslySetInnerHTML={{
            __html: mainContent || '<div></div>'
          }}></div>
        {hasSidebar &&
          (sidebarContent ? (
            <div
              style={sidebarOuterStyle}
              dangerouslySetInnerHTML={{
                __html: sidebarContent || '<div></div>'
              }}></div>
          ) : (
            <div style={sidebarOuterStyle}>
              <SidebarLayout sidebarStyle={sidebarStyle}>
                <SidebarInnerContainer
                  sidebarStyle={sidebarStyle}
                  palette={palette}></SidebarInnerContainer>
              </SidebarLayout>
            </div>
          ))}
      </ResumeLayout>
      {pageNumbers && (
        <PageNumber
          layout={layout}
          palette={palette}
          pageIndex={pageIndex}
          totalPages={totalPages}
        />
      )}
    </ResumePage>
  )
}
