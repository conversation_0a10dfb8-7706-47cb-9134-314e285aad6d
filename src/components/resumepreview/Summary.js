'use client'

import { convertLexicalToHtml } from '@utils'
import { RichText, SectionContainer, SectionTitle } from './ui'

export const Summary = ({ resumeData, tc, location }) => {
  const {
    summary,
    design: { layout, palette, headingUnderlineStyle }
  } = resumeData

  const htmlContent = convertLexicalToHtml(summary)

  if (!htmlContent) return null

  return (
    <SectionContainer layout={layout}>
      <SectionTitle
        title={tc('summary')}
        layout={layout}
        palette={palette}
        location={location}
        headingUnderlineStyle={headingUnderlineStyle}
      />
      <RichText
        palette={palette}
        location={location}
        htmlContent={htmlContent}
        className='section-content inner-content'
      />
    </SectionContainer>
  )
}
