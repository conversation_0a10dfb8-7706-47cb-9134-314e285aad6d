import {
  getColorTheme,
  resumeHasSidebar,
  resumeOptionValue,
  resumeOptions,
  resumeStyles
} from '@utils'

export const ResumePage = ({ resumeData, children }) => {
  const { design } = resumeData
  const { layout, palette, accent, backgroundPureWhite, font } = design

  const hasSidebar = resumeHasSidebar(layout)
  const pageStyle = resumeStyles.page(palette, backgroundPureWhite, font)
  const theme = getColorTheme(palette, 'sidebar')
  const accentStyles = resumeOptions.accent.styles[accent]

  return (
    <div style={pageStyle} className='shadow-resume-page border border-black/5 rounded'>
      {accent === resumeOptionValue.ribbon && !hasSidebar && (
        <div
          style={{
            backgroundColor: theme.fill,
            width: '100%',
            height: accentStyles.bandHeight,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            zIndex: 1
          }}
        />
      )}
      {accent === resumeOptionValue.blur && (
        // eslint-disable-next-line @next/next/no-img-element
        <img
          src={`/images/${accentStyles.backgroundImage}`}
          alt='blur'
          width={595}
          height={842}
          style={{
            position: 'absolute',
            inset: 0,
            width: '100%',
            height: '100%',
            zIndex: 0
          }}
        />
      )}
      <div style={{ position: 'relative', zIndex: 2, height: '100%' }}>{children}</div>
    </div>
  )
}
