'use client'

import { resumeHasSidebar, resumeOptionValue } from '@utils'
import { AdditionalProficiencies } from './AdditionalProficiencies'
import { CustomSections } from './CustomSections'
import { Education } from './Education'
import { Experience } from './Experience'
import { PersonalDetails } from './PersonalDetails'
import { Skills } from './Skills'
import { Summary } from './Summary'
import { MainContainer, SectionsContainer } from './ui'

const sectionComponents = {
  [resumeOptionValue.skills]: Skills,
  [resumeOptionValue.experience]: Experience,
  [resumeOptionValue.education]: Education,
  [resumeOptionValue.proficiencies]: AdditionalProficiencies,
  [resumeOptionValue.customSections]: CustomSections
}

export const ResumeMain = ({ resumeData, tc = {}, mt = {} }) => {
  const { design } = resumeData
  const { layout, sections, spacing } = design

  const hasSidebar = resumeHasSidebar(layout)

  if (!sections?.length) return null

  const mainSections = hasSidebar
    ? sections.filter((section) => section.isVisible && !section.showInSidebar)
    : sections.filter((section) => section.isVisible)

  return (
    <MainContainer hasSidebar={hasSidebar} layout={layout}>
      <PersonalDetails resumeData={resumeData} tc={tc} location='main' />
      <SectionsContainer spacing={spacing}>
        <Summary resumeData={resumeData} tc={tc} location='main' />
        {mainSections.map((section) => {
          const Component = sectionComponents[section.sectionKey]
          return Component ? (
            <Component
              key={section.id}
              resumeData={resumeData}
              tc={tc}
              mt={mt}
              location='main'
            />
          ) : null
        })}
      </SectionsContainer>
    </MainContainer>
  )
}
