'use client'

import { useEffect, useState } from 'react'
import { Copy, Download, EllipsisVertical, SquarePen, Trash } from 'lucide-react'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
  Text,
  TitleEdit,
  buttonVariants
} from '@components'
import { routes } from '@constants'
import { useResumes } from '@hooks'
import { cn } from '@utils'

export const ResumeMenu = ({ name, id, className, resume }) => {
  const router = useRouter()
  const tc = useTranslations('Common')
  const { setResumeId, createResume } = useResumes()
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    const down = (event) => {
      if (!isOpen) return

      if (event.key === 'e' && (event.metaKey || event.ctrlKey)) {
        event.preventDefault()
        router.push(routes.editor(id))
      }

      if (event.key === 'd' && (event.metaKey || event.ctrlKey)) {
        event.preventDefault()
        createResume(resume)
      }
    }

    document.addEventListener('keydown', down)
    return () => document.removeEventListener('keydown', down)
  }, [id, router, isOpen]) // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <div className={cn('absolute bottom-4 right-4 z-10', className)}>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger
          className={cn(
            buttonVariants({ variant: 'icon' }),
            'size-8 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-slate-300'
          )}>
          <EllipsisVertical size={16} />
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className='w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg'
          side='bottom'
          align='end'>
          <DropdownMenuLabel className='p-0'>
            <TitleEdit name={name} id={id} />
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem>
              <Download className='text-slate-600' />
              <Text as='span' className='leading-5 text-slate-950'>
                {tc('downloadPdf')}
              </Text>
              <DropdownMenuShortcut>⌘S</DropdownMenuShortcut>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href={routes.editor(id)}>
                <SquarePen className='text-slate-600' />
                <Text as='span' className='leading-5 text-slate-950'>
                  {tc('editResume')}
                </Text>
                <DropdownMenuShortcut>⌘E</DropdownMenuShortcut>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => createResume(resume)}>
              <Copy className='text-slate-600' />
              <Text as='span' className='leading-5 text-slate-950'>
                {tc('duplicate')}
              </Text>
              <DropdownMenuShortcut>⌘D</DropdownMenuShortcut>
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => setResumeId(id)}>
            <Trash className='text-red-500' />
            <Text as='span' className='text-red-500'>
              {tc('delete')}
            </Text>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
