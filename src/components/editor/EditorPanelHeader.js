'use client'

import { useCallback, useEffect, useRef, useState } from 'react'
import { SquarePen } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  Input,
  Text
} from '@components'
import { useResume } from '@hooks'

export const EditorPanelHeader = ({ sectionKey, description, editable = true }) => {
  const tc = useTranslations('Common')

  return (
    <div className='border-b border-slate-200 pb-4 mb-6'>
      <div className='flex items-center gap-1'>
        {editable ? (
          <TitleEditable sectionKey={sectionKey} />
        ) : (
          <Text
            as='span'
            variant='base'
            weight='semibold'
            className='block text-slate-700 whitespace-nowrap text-ellipsis overflow-hidden'>
            {tc(sectionKey)}
          </Text>
        )}
      </div>
      {description && (
        <Text as='span' variant='sm' className='block leading-5 text-slate-500 mt-1'>
          {tc(description)}
        </Text>
      )}
    </div>
  )
}

export const TitleEditable = ({ sectionKey }) => {
  const tc = useTranslations('Common')
  const [isEditSectionTitle, setIsEditSectionTitle] = useState(false)
  const { resume, updateResume } = useResume()
  const { design } = resume
  const formRef = useRef(null)

  const initialName =
    design?.sections.find((section) => section.sectionKey === sectionKey)?.sectionName ||
    ''

  const value = sectionKey === initialName ? tc(sectionKey) : initialName

  const form = useForm({
    defaultValues: {
      sectionName: value
    }
  })

  const { control, setFocus, handleSubmit, reset } = form

  const onSubmit = useCallback(
    (data) => {
      if (data.sectionName === tc(sectionKey)) {
        reset({ sectionName: value })
        setIsEditSectionTitle(false)
        return
      }

      if (data.sectionName && data.sectionName.trim() !== initialName.trim()) {
        const updatedSections = resume.design.sections.map((section) => {
          if (section.sectionKey === sectionKey) {
            return { ...section, sectionName: data.sectionName }
          }
          return section
        })
        updateResume({ design: { ...design, sections: updatedSections } })
      } else {
        reset({ sectionName: value })
      }
      setIsEditSectionTitle(false)
    },
    [
      initialName,
      resume.design,
      sectionKey,
      design,
      updateResume,
      reset,
      value,
      setIsEditSectionTitle,
      tc
    ]
  )

  useEffect(() => {
    if (isEditSectionTitle) {
      setFocus('sectionName')

      const handleClickOutside = (event) => {
        if (formRef.current && !formRef.current.contains(event.target)) {
          handleSubmit(onSubmit)()
        }
      }

      document.addEventListener('mousedown', handleClickOutside)

      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }
  }, [isEditSectionTitle, setFocus, handleSubmit, onSubmit])

  return (
    <>
      {isEditSectionTitle ? (
        <Form {...form}>
          <form
            ref={formRef}
            onSubmit={handleSubmit(onSubmit)}
            className='flex items-center w-full gap-1.5'>
            <FormField
              control={control}
              name='sectionName'
              render={({ field }) => (
                <FormItem className='w-full'>
                  <FormControl>
                    <Input
                      {...field}
                      autoComplete='off'
                      className='border-none rounded-none shadow-none bg-transparent p-0 text-base font-semibold text-slate-700 disabled:bg-transparent disabled:cursor-default placeholder:text-slate-700 disabled:text-slate-700'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      ) : (
        <Text
          as='span'
          variant='base'
          weight='semibold'
          className='block text-slate-700 whitespace-nowrap text-ellipsis overflow-hidden'>
          {value}
        </Text>
      )}
      {!isEditSectionTitle && (
        <Button
          type='button'
          onClick={() => setIsEditSectionTitle(true)}
          variant='icon'
          className='border-none shadow-none p-1'>
          <SquarePen className='text-slate-500' />
        </Button>
      )}
    </>
  )
}
