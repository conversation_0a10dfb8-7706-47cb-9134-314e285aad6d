'use client'

import { Text } from '@components'
import { CodeNode } from '@lexical/code'
import { LinkNode } from '@lexical/link'
import { ListItemNode, ListNode } from '@lexical/list'
import { TRANSFORMERS } from '@lexical/markdown'
import { LexicalComposer } from '@lexical/react/LexicalComposer'
import { ContentEditable } from '@lexical/react/LexicalContentEditable'
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary'
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin'
import { ListPlugin } from '@lexical/react/LexicalListPlugin'
import { MarkdownShortcutPlugin } from '@lexical/react/LexicalMarkdownShortcutPlugin'
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin'
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin'
import { HeadingNode, QuoteNode } from '@lexical/rich-text'
import { richTextTheme } from '@utils'
import { <PERSON><PERSON><PERSON><PERSON> } from './AiButton'

const editorConfig = {
  onError(error) {
    throw error
  },
  nodes: [HeadingNode, ListNode, ListItemNode, QuoteNode, CodeNode, LinkNode],
  theme: richTextTheme
}

function Placeholder({ placeholder }) {
  return (
    <Text
      as='span'
      variant='sm'
      className='text-slate-400 truncate absolute top-4 left-4 pl-px select-none inline-block pointer-events-none'>
      {placeholder}
    </Text>
  )
}

const handleLocalChange = (value, onChange) => {
  const editorStateJSON = value.toJSON()
  onChange(editorStateJSON)
}

export const RichTextField = ({ placeholder, onChange, value }) => {
  return (
    <LexicalComposer
      initialConfig={{
        ...editorConfig,
        editorState: value ? JSON.stringify(value) : null
      }}>
      <div className='relative overflow-hidden rounded-lg'>
        <RichTextPlugin
          contentEditable={
            <ContentEditable
              placeholder={<Placeholder placeholder={placeholder} />}
              className='min-h-36 max-h-96 overflow-y-auto px-4 pt-4 pb-16 thin-scrollbar bg-white border border-slate-300 shadow-sm focus:outline-none focus:border-blue-500 rounded-lg *:text-sm *:leading-4.5'
            />
          }
          ErrorBoundary={LexicalErrorBoundary}
        />
        <div className='w-auto bg-white group-disabled:bg-slate-50 absolute bottom-px left-4 right-4'>
          <div className='border-t py-3 border-slate-200'>
            <AiButton />
          </div>
        </div>
      </div>
      <ListPlugin />
      <MarkdownShortcutPlugin transformers={TRANSFORMERS} />
      <OnChangePlugin
        onChange={(value) => handleLocalChange(value, onChange)}
        ignoreSelectionChange={true}
      />
      <HistoryPlugin />
    </LexicalComposer>
  )
}
