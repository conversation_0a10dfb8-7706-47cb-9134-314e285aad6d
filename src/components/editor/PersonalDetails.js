'use client'

import { Plus, X } from 'lucide-react'
import { AnimatePresence, motion } from 'motion/react'
import { useTranslations } from 'next-intl'
import { Controller, useFieldArray, useForm } from 'react-hook-form'
import {
  <PERSON><PERSON>,
  EditorFieldCheckbox,
  EditorFieldItem,
  EditorFieldLabel,
  EditorFieldRow,
  EditorFormBlock,
  EditorFormFieldGroup,
  EditorFormTitle,
  EditorPanelHeader,
  Form,
  Input,
  ProfileImageController,
  RichTextFieldController,
  Switch
} from '@components'
import { zodResolver } from '@hookform/resolvers/zod'
import { useFieldRemove, useFormWatch, useResume } from '@hooks'
import {
  newSocialLink,
  personalDetailsSchema,
  resumeOptionValue,
  resumeValues
} from '@utils'

export const PersonalDetails = () => {
  const tc = useTranslations('Common')
  const { resume } = useResume()

  const form = useForm({
    resolver: zodResolver(personalDetailsSchema),
    defaultValues: resumeValues.personalDetails(resume)
  })

  const { register, watch, control } = form
  const data = resume.links

  const { fields, append, remove } = useFieldArray({
    control: control,
    name: 'links'
  })

  useFormWatch(watch, resumeOptionValue.personalDetails)

  const handleRemove = useFieldRemove({
    remove,
    sectionKey: 'links',
    data
  })

  return (
    <>
      <EditorPanelHeader
        editable={false}
        sectionKey={resumeOptionValue.personalDetails}
        description='yourNameSummaryImageAndTitle'
      />
      <Form {...form}>
        <form className='space-y-6 divide-y divide-slate-200 [&>*:not(:first-child)]:pt-6'>
          <EditorFormBlock>
            <EditorFormTitle title={tc('yourDetails')} />
            <EditorFieldRow className='gap-6'>
              <div className='flex shrink-0 flex-col gap-2'>
                <EditorFieldItem>
                  <EditorFieldLabel className='sr-only' htmlFor='profileImage'>
                    {tc('addAProfileImage')}
                  </EditorFieldLabel>
                  <ProfileImageController
                    name='profileImage'
                    control={form.control}
                    profileImageUrl={resume?.profileImage?.url}
                  />
                </EditorFieldItem>
                <EditorFieldItem>
                  <EditorFieldCheckbox>
                    <Controller
                      name='showProfileImage'
                      control={form.control}
                      render={({ field }) => (
                        <Switch
                          id='showProfileImage'
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          name={field.name}
                          ref={field.ref}
                        />
                      )}
                    />
                    <EditorFieldLabel htmlFor='showProfileImage'>
                      {tc('show')}
                    </EditorFieldLabel>
                  </EditorFieldCheckbox>
                </EditorFieldItem>
              </div>
              <EditorFormFieldGroup>
                <EditorFieldRow className='flex-row'>
                  <EditorFieldItem>
                    <EditorFieldLabel htmlFor='firstName'>
                      {tc('firstName')}
                    </EditorFieldLabel>
                    <Input
                      type='text'
                      placeholder={tc('firstName')}
                      {...register(`firstName`)}
                    />
                  </EditorFieldItem>
                  <EditorFieldItem>
                    <EditorFieldLabel htmlFor='lastName'>
                      {tc('lastName')}
                    </EditorFieldLabel>
                    <Input
                      type='text'
                      placeholder={tc('lastName')}
                      {...register(`lastName`)}
                    />
                  </EditorFieldItem>
                </EditorFieldRow>
                <EditorFieldRow>
                  <EditorFieldItem>
                    <EditorFieldLabel htmlFor='jobTitle'>
                      {tc('desiredJobTitle')}
                    </EditorFieldLabel>
                    <Input
                      type='text'
                      placeholder={tc('desiredJobTitle')}
                      {...register(`jobTitle`)}
                    />
                  </EditorFieldItem>
                </EditorFieldRow>
              </EditorFormFieldGroup>
            </EditorFieldRow>
          </EditorFormBlock>
          <EditorFormBlock>
            <EditorFormTitle title={tc('summary')} />
            <EditorFormFieldGroup>
              <EditorFieldRow>
                <EditorFieldItem>
                  <EditorFieldLabel className='sr-only' htmlFor='summary'>
                    {tc('summary')}
                  </EditorFieldLabel>
                  <RichTextFieldController
                    name='summary'
                    control={form.control}
                    placeholder={tc('summary')}
                  />
                </EditorFieldItem>
              </EditorFieldRow>
            </EditorFormFieldGroup>
          </EditorFormBlock>
          <EditorFormBlock>
            <EditorFormTitle title={tc('contactDetails')} />
            <EditorFormFieldGroup>
              <EditorFieldRow>
                <EditorFieldItem>
                  <EditorFieldLabel htmlFor='phone'>{tc('phone')}</EditorFieldLabel>
                  <Input type='text' placeholder={tc('phone')} {...register(`phone`)} />
                </EditorFieldItem>
                <EditorFieldItem>
                  <EditorFieldLabel htmlFor='email'>{tc('email')}</EditorFieldLabel>
                  <Input type='text' placeholder={tc('email')} {...register(`email`)} />
                </EditorFieldItem>
              </EditorFieldRow>
            </EditorFormFieldGroup>
          </EditorFormBlock>
          <EditorFormBlock>
            <EditorFormTitle title={tc('links')} />
            <EditorFormFieldGroup>
              <AnimatePresence>
                {fields.map((field, index) => (
                  <motion.div
                    key={field.id}
                    layout='position'
                    layoutId={field.id}
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{
                      id: 'exit-animation',
                      opacity: 0
                    }}
                    onAnimationComplete={(definition) => {
                      if (
                        index === fields.length - 1 &&
                        typeof definition === 'object' &&
                        definition !== null &&
                        'id' in definition &&
                        definition.id === 'exit-animation'
                      ) {
                        remove(index)
                      }
                    }}
                    className='overflow-hidden'>
                    <EditorFieldRow className='flex-col sm:flex-row'>
                      <EditorFieldItem>
                        <EditorFieldLabel htmlFor={`links.${index}.name`}>
                          {tc('name')}
                        </EditorFieldLabel>
                        <Input
                          type='text'
                          placeholder={tc('name')}
                          {...register(`links.${index}.name`)}
                        />
                      </EditorFieldItem>
                      <EditorFieldItem>
                        <EditorFieldLabel htmlFor={`links.${index}.url`}>
                          {tc('url')}
                        </EditorFieldLabel>
                        <Input
                          type='url'
                          placeholder={tc('url')}
                          {...register(`links.${index}.url`)}
                        />
                      </EditorFieldItem>
                      <EditorFieldItem className='max-w-4 mt-8'>
                        <Button
                          type='button'
                          variant='subtle'
                          size='icon'
                          onClick={() => handleRemove(index)}>
                          <X size={16} className='mt-6' />
                        </Button>
                      </EditorFieldItem>
                    </EditorFieldRow>
                  </motion.div>
                ))}
              </AnimatePresence>
            </EditorFormFieldGroup>
            <Button
              type='button'
              variant='subtle'
              size='link'
              onClick={() => append(newSocialLink)}
              className='mt-3'>
              {tc('addLink')}
              <Plus size={16} />
            </Button>
          </EditorFormBlock>
        </form>
      </Form>
    </>
  )
}
