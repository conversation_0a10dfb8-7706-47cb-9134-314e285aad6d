'use client'

import { useEffect } from 'react'
import { useDragControls } from 'motion/react'
import { useTranslations } from 'next-intl'
import { Controller, useFieldArray, useForm, useWatch } from 'react-hook-form'
import {
  AccordionWrapper,
  AddSection,
  DeleteSectionItem,
  EditorFieldCheckbox,
  EditorFieldItem,
  EditorFieldLabel,
  EditorFieldRow,
  EditorFormBlock,
  EditorFormFieldGroup,
  Form,
  Input,
  MonthPicker,
  ReorderGroup,
  RichTextFieldController,
  SectionContent,
  SectionItem,
  SectionReorder,
  SectionTrigger,
  SectionVisibilityHandler,
  Switch,
  YearPicker
} from '@components'
import { zodResolver } from '@hookform/resolvers/zod'
import { useEditor, useFieldAdd, useFieldRemove, useFormWatch, useResume } from '@hooks'
import {
  handleSectionReorder,
  newProficiencies,
  resumeOptionValue,
  resumeSectionSchemas,
  resumeValues
} from '@utils'
import { EditorPanelHeader } from './EditorPanelHeader'

export const Proficiencies = () => {
  const { resume, updateResume } = useResume()
  const sectionKey = resumeOptionValue.proficiencies

  const data = resume[sectionKey]

  const { visiblityControls } = useEditor()

  const form = useForm({
    resolver: zodResolver(resumeSectionSchemas.proficiencies),
    defaultValues: resumeValues.proficiencies(resume, sectionKey),
    mode: 'onChange'
  })

  const { register, watch, control, trigger, unregister } = form

  const { fields, append, remove, move } = useFieldArray({
    control: control,
    name: sectionKey
  })

  useFormWatch(watch, sectionKey, trigger)

  const handleRemove = useFieldRemove({
    remove,
    sectionKey: sectionKey,
    data
  })

  const handleAdd = useFieldAdd({
    append,
    sectionKey: sectionKey,
    data,
    newItem: newProficiencies
  })

  const handleReorder = (newOrder) => {
    handleSectionReorder(newOrder, fields, move, updateResume, sectionKey)
  }

  return (
    <>
      <EditorPanelHeader sectionKey={sectionKey} description='certificatesAndLanguages' />
      <Form {...form}>
        <form>
          {!visiblityControls && <AddSection text='addProficiency' onClick={handleAdd} />}
          <AccordionWrapper fields={fields}>
            <ReorderGroup values={fields} onReorder={handleReorder}>
              {fields.map((field, index) => {
                return (
                  <ProficienciesAccordion
                    key={field.id}
                    form={form}
                    field={field}
                    index={index}
                    remove={handleRemove}
                    register={register}
                    unregister={unregister}
                  />
                )
              })}
            </ReorderGroup>
          </AccordionWrapper>
        </form>
      </Form>
    </>
  )
}

const ProficienciesAccordion = ({ form, field, index, remove, register, unregister }) => {
  const tc = useTranslations('Common')
  const controls = useDragControls()

  useEffect(() => {
    register(`proficiencies.${index}.startMonth`, { value: field.startMonth })
    register(`proficiencies.${index}.startYear`, { value: field.startYear })
    register(`proficiencies.${index}.showDate`, { value: field.showDate })
    register(`proficiencies.${index}.description`, { value: field.description })

    return () => {
      unregister([
        `proficiencies.${index}.startMonth`,
        `proficiencies.${index}.startYear`,
        `proficiencies.${index}.showDate`,
        `proficiencies.${index}.description`
      ])
    }
  }, [unregister, index, register]) // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <SectionItem id={field.id} value={field} dragListener={false} dragControls={controls}>
      <SectionPanel
        field={field}
        index={index}
        controls={controls}
        remove={remove}
        form={form}
      />
      <SectionContent>
        <EditorFormBlock>
          <EditorFormFieldGroup>
            <EditorFieldRow>
              <EditorFieldItem>
                <EditorFieldLabel htmlFor={`proficiencies.${index}.title`}>
                  {tc('title')}
                </EditorFieldLabel>
                <Input
                  type='text'
                  placeholder={tc('title')}
                  {...register(`proficiencies.${index}.title`)}
                />
              </EditorFieldItem>
            </EditorFieldRow>
            <EditorFieldRow>
              <EditorFieldItem>
                <EditorFieldLabel htmlFor={`proficiencies.${index}.subTitle`}>
                  {tc('subTitle')}
                </EditorFieldLabel>
                <Input
                  type='text'
                  placeholder={tc('subTitle')}
                  {...register(`proficiencies.${index}.subTitle`)}
                />
              </EditorFieldItem>
            </EditorFieldRow>
          </EditorFormFieldGroup>
          <EditorFormFieldGroup>
            <EditorFieldItem>
              <EditorFieldLabel>{tc('date')}</EditorFieldLabel>
              <EditorFieldRow className='flex-row items-center flex-wrap'>
                <EditorFieldItem className='max-w-32'>
                  <Controller
                    name={`proficiencies.${index}.startMonth`}
                    control={form.control}
                    render={({ field }) => (
                      <MonthPicker
                        id={`proficiencies.${index}.startMonth`}
                        label={tc('startMonth')}
                        placeholder={tc('month')}
                        value={field.value}
                        onChange={field.onChange}
                        name={field.name}
                        ref={field.ref}
                      />
                    )}
                  />
                </EditorFieldItem>
                <EditorFieldItem className='max-w-32'>
                  <Controller
                    name={`proficiencies.${index}.startYear`}
                    control={form.control}
                    render={({ field }) => (
                      <YearPicker
                        id={`proficiencies.${index}.startYear`}
                        label={tc('startYear')}
                        placeholder={tc('year')}
                        value={field.value}
                        onChange={field.onChange}
                        name={field.name}
                        ref={field.ref}
                      />
                    )}
                  />
                </EditorFieldItem>
                <EditorFieldItem className='w-auto'>
                  <EditorFieldCheckbox>
                    <Controller
                      name={`proficiencies.${index}.showDate`}
                      control={form.control}
                      render={({ field }) => (
                        <Switch
                          id={`proficiencies.${index}.showDate`}
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          name={field.name}
                          ref={field.ref}
                        />
                      )}
                    />
                    <EditorFieldLabel htmlFor={`proficiencies.${index}.showDate`}>
                      {tc('showDate')}
                    </EditorFieldLabel>
                  </EditorFieldCheckbox>
                </EditorFieldItem>
              </EditorFieldRow>
            </EditorFieldItem>
          </EditorFormFieldGroup>
          <EditorFormFieldGroup>
            <EditorFieldRow>
              <EditorFieldItem>
                <EditorFieldLabel htmlFor={`proficiencies.${index}.description`}>
                  {tc('description')}
                </EditorFieldLabel>
                <RichTextFieldController
                  name={`proficiencies.${index}.description`}
                  control={form.control}
                  placeholder={tc('describeYourCertification')}
                />
              </EditorFieldItem>
            </EditorFieldRow>
          </EditorFormFieldGroup>
        </EditorFormBlock>
      </SectionContent>
    </SectionItem>
  )
}

const SectionPanel = ({ field, index, controls, remove, form }) => {
  const tc = useTranslations('Common')
  const { visiblityControls, sectionsVisibility, updateSectionsVisibility } = useEditor()

  const eyeVisible = sectionsVisibility[index] || false

  const currentValue = useWatch({
    control: form.control,
    name: `proficiencies.${index}`
  })

  const sectionTitle = currentValue?.title || tc('newProficiency')
  const isVisible = currentValue?.isVisible || false

  function deleteItemHandler() {
    remove(index)
    updateSectionsVisibility(field, index, 'remove')
  }
  return (
    <div className='px-4 flex items-center gap-1.5'>
      {visiblityControls ? (
        <SectionVisibilityHandler
          isVisible={eyeVisible}
          onClick={() => updateSectionsVisibility(field, index)}
        />
      ) : (
        <SectionReorder dragHandler={(event) => controls.start(event)} />
      )}
      <SectionTrigger label={sectionTitle} isVisible={isVisible} />
      <DeleteSectionItem
        orignalValue={newProficiencies}
        currentValue={currentValue}
        deleteItemHandler={deleteItemHandler}
      />
    </div>
  )
}
