'use client'

import { SwatchBook } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { Button } from '@components'

export const Templates = () => {
  const tc = useTranslations('Common')
  return (
    <Button variant='subtle' size='link' className='gap-1 text-slate-700 p-0'>
      <SwatchBook size={16} className='text-slate-500' />
      <span className='hidden lg:block'>{tc('templates')}</span>
    </Button>
  )
}
