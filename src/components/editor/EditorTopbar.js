import { SquarePen } from 'lucide-react'
import { Text } from '@components'

export const EditorTopbar = ({ children }) => {
  return (
    <div className='bg-white border-b border-slate-200 flex items-center justify-between p-4 lg:p-5'>
      {children}
    </div>
  )
}

export const ResumeTitle = ({ title }) => {
  return (
    <div className='flex items-center gap-1'>
      <Text
        variant='base'
        weight='semibold'
        className='max-w-40 w-full text-slate-700 truncate'>
        {title}
      </Text>
      <SquarePen size={20} className='text-slate-500' />
    </div>
  )
}
