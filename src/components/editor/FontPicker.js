'use client'

import { useTranslations } from 'next-intl'
import {
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@components'

export const FontPicker = ({ optionsData, value, onChange }) => {
  const { options } = optionsData
  return (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger className='max-w-52 w-full'>
        <SelectValue placeholder='Select a font' />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem
            style={{ fontFamily: option.fontFamily, fontWeight: 500 }}
            key={option.value}
            value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}

export const FontPickerLabel = ({ label }) => {
  const tc = useTranslations('Common')
  return (
    <Label variant='sm' weight='medium' className='text-slate-700 mb-1.5'>
      {tc(label)}
    </Label>
  )
}
