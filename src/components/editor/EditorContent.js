import { resumeOptionValue } from '@utils'
import { CustomSection } from './CustomSection'
import { Education } from './Education'
import { Experience } from './Experience'
import { PersonalDetails } from './PersonalDetails'
import { Proficiencies } from './Proficiencies'
import { Skills } from './Skills'

export const EditorContent = ({ activePanelId }) => {
  switch (activePanelId) {
    case resumeOptionValue.personalDetails: {
      return <PersonalDetails />
    }
    case resumeOptionValue.experience: {
      return <Experience />
    }
    case resumeOptionValue.skills: {
      return <Skills />
    }
    case resumeOptionValue.education: {
      return <Education />
    }
    case resumeOptionValue.proficiencies: {
      return <Proficiencies />
    }
    case resumeOptionValue.customSections: {
      return <CustomSection />
    }
    default: {
      return null
    }
  }
}
