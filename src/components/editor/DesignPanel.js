'use client'

import { AnimatePresence, motion } from 'motion/react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import {
  ColorPicker,
  EditorFormBlock,
  EditorFormFieldGroup,
  EditorNavigation,
  EditorPanelHeader,
  FancyAvtarGroup,
  FancyRadioGroup,
  FontPicker,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Switch
} from '@components'
import { zodResolver } from '@hookform/resolvers/zod'
import { useFormWatch, useResume } from '@hooks'
import {
  resumeHasSidebar,
  resumeItemSchemas,
  resumeOptionValue,
  resumeOptions,
  resumeValues
} from '@utils'

export const DesignPanel = () => {
  const tc = useTranslations('Common')
  const sectionKey = resumeOptionValue.design

  const { resume } = useResume()

  const {
    design: { layout }
  } = resume

  const form = useForm({
    resolver: zodResolver(resumeItemSchemas.design),
    defaultValues: resumeValues.design(resume)
  })

  const { watch, trigger } = form

  useFormWatch(watch, sectionKey, trigger)

  const isWithSidebar = resumeHasSidebar(layout)

  const accentOptionData = resumeOptions.accent

  const excludedAccentValues = new Set([
    resumeOptionValue.header,
    resumeOptionValue.ribbon
  ])

  const filteredOptions = accentOptionData.options.filter(
    (option) => !excludedAccentValues.has(option.value)
  )

  const accentOptions = isWithSidebar
    ? { ...accentOptionData, options: filteredOptions }
    : accentOptionData

  return (
    <div className='h-full flex flex-col overflow-hidden'>
      <EditorNavigation />
      <Form {...form}>
        <form className='space-y-8 relative overflow-y-auto thin-scrollbar p-4 lg:p-6'>
          <EditorFormBlock className='space-y-0'>
            <EditorPanelHeader sectionKey='layout' editable={false} />
            <EditorFormFieldGroup className='space-y-6'>
              <FormField
                control={form.control}
                name='layout'
                render={({ field }) => {
                  return (
                    <FormItem className='space-y-3'>
                      <FormLabel variant='sm' className='text-slate-700'>
                        {tc('resumeLayout')}
                      </FormLabel>
                      <FormControl>
                        <FancyRadioGroup
                          name='resumeLayout'
                          optionsData={resumeOptions.layout}
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )
                }}
              />
              <AnimatePresence>
                {isWithSidebar && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{
                      height: 'auto',
                      opacity: 1,
                      transition: {
                        opacity: {
                          duration: 0.25,
                          ease: 'easeInOut'
                        }
                      }
                    }}
                    exit={{
                      height: 0,
                      opacity: 0,
                      transition: {
                        opacity: {
                          duration: 0.2,
                          ease: 'easeOut'
                        }
                      }
                    }}>
                    <FormField
                      control={form.control}
                      name='sidebarStyle'
                      render={({ field }) => {
                        return (
                          <FormItem className='space-y-3'>
                            <FormLabel variant='sm' className='text-slate-700'>
                              {tc('sidebarStyle')}
                            </FormLabel>
                            <FormControl>
                              <FancyRadioGroup
                                name='sidebarStyle'
                                optionsData={resumeOptions.sidebarStyle}
                                value={field.value}
                                onChange={field.onChange}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )
                      }}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
              <FormField
                control={form.control}
                name='spacing'
                render={({ field }) => {
                  return (
                    <FormItem className='space-y-3'>
                      <FormLabel variant='sm' className='text-slate-700'>
                        {tc('spacing')}
                      </FormLabel>
                      <FormControl>
                        <FancyRadioGroup
                          name='spacing'
                          optionsData={resumeOptions.spacing}
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )
                }}
              />
              <FormField
                control={form.control}
                name='accent'
                render={({ field }) => {
                  return (
                    <FormItem className='space-y-3'>
                      <FormLabel variant='sm' className='text-slate-700'>
                        {tc('accents')}
                      </FormLabel>
                      <FormControl>
                        <FancyRadioGroup
                          name='accents'
                          optionsData={accentOptions}
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )
                }}
              />
            </EditorFormFieldGroup>
          </EditorFormBlock>
          <EditorFormBlock className='space-y-0'>
            <EditorPanelHeader sectionKey='text' editable={false} />
            <EditorFormFieldGroup className='space-y-6'>
              <FormField
                control={form.control}
                name='font'
                render={({ field }) => {
                  return (
                    <FormItem className='space-y-3'>
                      <FormLabel variant='sm' className='text-slate-700'>
                        {tc('font')}
                      </FormLabel>
                      <FormControl>
                        <FontPicker
                          optionsData={resumeOptions.font}
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )
                }}
              />
              <FormField
                control={form.control}
                name='headingUnderlineStyle'
                render={({ field }) => {
                  return (
                    <FormItem className='space-y-3'>
                      <FormLabel variant='sm' className='text-slate-700'>
                        {tc('headingStyle')}
                      </FormLabel>
                      <FormControl>
                        <FancyRadioGroup
                          name='headingStyle'
                          optionsData={resumeOptions.headingUnderlineStyle}
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )
                }}
              />
            </EditorFormFieldGroup>
          </EditorFormBlock>
          <EditorFormBlock className='space-y-0'>
            <EditorPanelHeader sectionKey='colors' editable={false} />
            <EditorFormFieldGroup className='space-y-6'>
              <FormField
                control={form.control}
                name='palette'
                render={({ field }) => {
                  return (
                    <FormItem className='space-y-3'>
                      <FormLabel variant='sm' className='text-slate-700'>
                        {tc('palette')}
                      </FormLabel>
                      <FormControl>
                        <ColorPicker value={field.value} onChange={field.onChange} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )
                }}
              />
              <FormField
                control={form.control}
                name='backgroundPureWhite'
                render={({ field }) => (
                  <FormItem className='space-y-0 flex items-center gap-2'>
                    <FormControl>
                      <Switch checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                    <FormLabel className='text-slate-700'>
                      {tc('pureWhiteBackground')}
                    </FormLabel>
                  </FormItem>
                )}
              />
            </EditorFormFieldGroup>
          </EditorFormBlock>
          <EditorFormBlock className='space-y-0'>
            <EditorPanelHeader sectionKey='additionalOptions' editable={false} />
            <EditorFormFieldGroup className='space-y-6'>
              {resume?.showProfileImage && resume?.profileImage && (
                <FormField
                  control={form.control}
                  name='avatarStyle'
                  render={({ field }) => {
                    return (
                      <FormItem className='space-y-3'>
                        <FormLabel variant='sm' className='text-slate-700'>
                          {tc('avatarStyle')}
                        </FormLabel>
                        <FormControl>
                          <FancyAvtarGroup
                            optionsData={resumeOptions.avatarStyle}
                            value={field.value}
                            onChange={field.onChange}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )
                  }}
                />
              )}
              <FormField
                control={form.control}
                name='pageNumbers'
                render={({ field }) => (
                  <FormItem className='space-y-0 flex items-center gap-2'>
                    <FormControl>
                      <Switch checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                    <FormLabel className='text-slate-700'>{tc('pageNumbers')}</FormLabel>
                  </FormItem>
                )}
              />
            </EditorFormFieldGroup>
          </EditorFormBlock>
        </form>
      </Form>
    </div>
  )
}
