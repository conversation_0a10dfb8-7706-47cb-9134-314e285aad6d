'use client'

import { useTranslations } from 'next-intl'
import { BreadcrumbPage } from '@components'
import { useResume } from '@hooks'

export const BreadcrumbResumeName = () => {
  const tc = useTranslations('Common')
  const {
    resume: { jobTitle }
  } = useResume()

  const displayName = jobTitle || tc('newResume')

  return <BreadcrumbPage className='truncate'>{displayName}</BreadcrumbPage>
}
