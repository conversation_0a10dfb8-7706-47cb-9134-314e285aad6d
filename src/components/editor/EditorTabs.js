'use client'

import { useMemo, useState } from 'react'
import { Brush, SquarePen } from 'lucide-react'
import { useTranslations } from 'next-intl'
import {
  DesignPanel,
  Drawer,
  DrawerContent,
  DrawerDescription,
  Drawer<PERSON>eader,
  <PERSON>er<PERSON><PERSON>le,
  EditorPanel,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from '@components'
import { useEditor } from '@hooks'
import { useMediaQuery } from '@raddix/use-media-query'
import { cn } from '@utils'

const tabs = [
  {
    label: 'editor',
    icon: SquarePen,
    component: () => <EditorPanel />
  },
  {
    label: 'design',
    icon: Brush,
    component: () => <DesignPanel />
  }
]

const TabList = () => {
  const tc = useTranslations('Common')
  return (
    <TabsList className='flex-shrink-0 px-4 lg:px-4.5 flex gap-4 border-b border-slate-300'>
      {tabs.map((tab) => (
        <TabsTrigger
          key={tab.label}
          value={tab.label}
          className='h-14 px-1 gap-1 w-1/2 sm:w-auto'>
          <tab.icon size={16} />
          {tc(tab.label)}
        </TabsTrigger>
      ))}
    </TabsList>
  )
}

const TabContent = ({ activeTab }) =>
  tabs.map((tab) => {
    const TabComponent = tab.component
    return (
      <TabsContent
        forceMount={true}
        key={`${tab.label}-panel`}
        value={tab.label}
        className={cn('mt-0 overflow-y-auto thin-scrollbar h-full', {
          block: tab.label === activeTab,
          hidden: tab.label !== activeTab
        })}>
        <TabComponent />
      </TabsContent>
    )
  })

const TabContainer = ({ activeTab, setActiveTab, className, children }) => (
  <Tabs
    defaultValue='editor'
    className={className}
    value={activeTab}
    onValueChange={(value) => setActiveTab(value)}>
    {children}
  </Tabs>
)

const snapPoints = ['300px', 1]

export const EditorTabs = () => {
  const [snap, setSnap] = useState(snapPoints[0])
  const { activeTab, setActiveTab } = useEditor()

  const tabList = useMemo(
    () => <TabList activeTab={activeTab} setActiveTab={setActiveTab} />,
    [activeTab, setActiveTab]
  )

  const tabContent = useMemo(() => <TabContent activeTab={activeTab} />, [activeTab])

  const isDesktop = useMediaQuery('(min-width: 1024px)')
  return isDesktop ? (
    <>
      <TabContainer
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        className='h-full hidden flex-col lg:flex'>
        {tabList}
        {tabContent}
      </TabContainer>
    </>
  ) : (
    <>
      <Drawer
        open={true}
        snapPoints={snapPoints}
        activeSnapPoint={snap}
        setActiveSnapPoint={setSnap}
        modal={false}>
        <DrawerContent className='h-full max-h-[85%] outline-none'>
          <DrawerHeader className='sr-only'>
            <DrawerTitle>Editor</DrawerTitle>
            <DrawerDescription>Editor</DrawerDescription>
          </DrawerHeader>
          <div
            className='overflow-y-auto thin-scrollbar h-full'
            onPointerDown={(event) => event.stopPropagation()}
            onTouchStart={(event) => event.stopPropagation()}>
            <TabContainer
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              className='h-full flex flex-col'>
              {tabList}
              {tabContent}
            </TabContainer>
          </div>
        </DrawerContent>
      </Drawer>
    </>
  )
}
