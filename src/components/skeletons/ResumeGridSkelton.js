import { Skeleton } from '@components'
import { cn } from '@utils'

export const ResumeGridSkelton = ({ className, items = 4 }) => {
  return (
    <div className='pt-6 pb-11'>
      <div className='inner-container'>
        <Skeleton className='h-6 w-28 mb-5' />
        <div
          className={cn(
            'grid grid-cols-1 gap-10',
            `sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-${items}`,
            className
          )}>
          {Array.from({ length: items }).map((_, index) => (
            <ResumeCardSkelton key={index} />
          ))}
        </div>
      </div>
    </div>
  )
}

export const ResumeCardSkelton = () => {
  return (
    <div className='border border-slate-200 rounded-lg flex flex-col gap-4 p-1.5'>
      <Skeleton className='h-36 w-full rounded-lg border-1 border-slate-200' />
      <div className='flex items-start justify-between gap-2'>
        <div className='flex flex-col gap-0.5 w-full'>
          <Skeleton className='h-5 w-full' />
          <Skeleton className='h-4 w-full' />
        </div>
        <div className='flex gap-2'>
          <Skeleton className='size-8' />
        </div>
      </div>
    </div>
  )
}
