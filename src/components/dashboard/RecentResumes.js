import { getTranslations } from 'next-intl/server'
import { ResumeCard, Text } from '@components'
import { getUserResumes } from '@data'
import { NoResumes } from './NoResumes'

export async function RecentResumes() {
  const tc = await getTranslations('Common')
  const { resumes } = await getUserResumes({
    query: {
      sort: '-updatedAt',
      limit: 4
    }
  })

  return (
    <>
      {resumes?.length > 0 ? (
        <section className='bg-slate-50 pt-6 pb-11 border-b border-x border-slate-200'>
          <div className='inner-container'>
            <Text
              as='h2'
              variant='base'
              weight='medium'
              className='flex items-center gap-2 mb-5 text-slate-700'>
              {tc('recents')}
            </Text>
            <div className='grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-10'>
              {resumes.map((resume) => (
                <ResumeCard key={resume.id} resume={resume} />
              ))}
            </div>
          </div>
        </section>
      ) : (
        <NoResumes />
      )}
    </>
  )
}
