'use client'

import { useRef, useState } from 'react'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Button,
  Input,
  Label,
  Text,
  buttonVariants
} from '@components'
import { collections, validationSchemas } from '@constants'
import { createMedia, deleteMediaById, updateMediaById } from '@data'
import { useAuth } from '@hooks'
import { allowedFileTypes, cn, getThumbnailUrl } from '@utils'

export const ProfilePicture = () => {
  const { currentUser, updateUserById } = useAuth()
  const tc = useTranslations('Common')
  const t = useTranslations('Errors')
  const [isLoading, setIsLoading] = useState(false)
  const fileInput = useRef(null)

  const [avatarUrl, setAvatarUrl] = useState(currentUser?.avatarUrl || '#')

  const validImageTypes = allowedFileTypes()

  const handleAvatarChange = async (event) => {
    const file = event.target.files[0]
    if (!file) return
    const validation = validationSchemas.file.safeParse(file)

    if (!validation.success) {
      const errorMessage = validation.error.issues[0].message
      const params =
        errorMessage === 'fileTooLarge'
          ? {
              maxFileSize: collections.media.maxFileSizeMb
            }
          : {
              fileTypes: validImageTypes
            }
      toast.error(t(errorMessage, params))
      fileInput.current.value = ''
      return
    }

    setIsLoading(true)
    try {
      setAvatarUrl(URL.createObjectURL(file))
      toast.promise(
        async () => {
          const media = currentUser?.avatarId
            ? await updateMediaById(currentUser.avatarId, file, currentUser.id)
            : await createMedia(file, currentUser.id)

          if (media?.url) {
            await updateUserById({
              id: currentUser.id,
              avatarUrl: getThumbnailUrl(media),
              avatarId: media.id
            })
          } else {
            setAvatarUrl('#')
          }
        },
        {
          loading: tc('uploading') + '...',
          success: tc('profilePictureUpdated'),
          error: t('updateFailed')
        }
      )
    } catch (error) {
      console.error('Upload failed:', error)
    } finally {
      setIsLoading(false)
      fileInput.current.value = ''
    }
  }

  const handleDeleteAvatar = async () => {
    setIsLoading(true)
    try {
      toast.promise(
        async () => {
          setAvatarUrl('#')
          await updateUserById({
            id: currentUser.id,
            avatarUrl: null,
            avatarId: null
          })
        },
        {
          loading: tc('removing') + '...',
          success: () => {
            if (currentUser.avatarId) {
              deleteMediaById(currentUser.avatarId)
            }
            return tc('profilePictureRemoved')
          },
          error: t('failedToRemoveProfilePicture')
        }
      )
    } catch (error) {
      console.error('Delete failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div>
      <Text
        variant='xs'
        weight='medium'
        as='span'
        className='block text-slate-600 mb-2.5'>
        {tc('profilePicture')}
      </Text>
      <div className='space-y-6'>
        <div className='flex items-center gap-5'>
          <Avatar className='size-16'>
            <AvatarImage
              className='object-cover'
              src={avatarUrl}
              alt={currentUser?.firstName || ''}
            />

            <AvatarFallback className='uppercase'>
              {currentUser?.firstName?.charAt(0) || ''}
              {currentUser?.lastName?.charAt(0) || ''}
            </AvatarFallback>
          </Avatar>
          <div className='flex items-center gap-2'>
            <div className='relative'>
              <Label
                className={cn(
                  buttonVariants({ variant: 'secondary', size: 'sm' }),
                  'cursor-pointer',
                  isLoading && 'opacity-50 pointer-events-none'
                )}
                htmlFor='avatar'>
                {tc('change')}
              </Label>
              <Input
                className='absolute inset-0 opacity-0 -z-10'
                type='file'
                id='avatar'
                name='avatar'
                accept='image/*'
                onChange={handleAvatarChange}
                ref={fileInput}
              />
            </div>
            {currentUser?.avatarUrl && (
              <Button
                variant='white'
                size='sm'
                onClick={handleDeleteAvatar}
                isDisabled={isLoading}>
                {tc('delete')}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
