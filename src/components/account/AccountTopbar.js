import { Settings } from 'lucide-react'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, Text } from '@components'
import { routes } from '@constants'

export const AccountTopbar = () => {
  const t = useTranslations('Common')
  return (
    <div className='inner-container'>
      <div className='flex-shrink-0 flex flex-col pt-5'>
        <div className='flex items-center gap-1 mb-2.5'>
          <Settings size={24} className='text-slate-700' />
          <Text variant='xl' as='h2' className='text-slate-700'>
            {t('settings')}
          </Text>
        </div>
        <div className='border-b border-slate-300'>
          <Tabs defaultValue='account'>
            <TabsList>
              <TabsTrigger asChild value='account' className='px-1 py-4.5'>
                <Link href={routes.account}>{t('account')}</Link>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
