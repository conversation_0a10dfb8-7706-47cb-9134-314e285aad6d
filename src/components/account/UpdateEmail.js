'use client'

import { AnimatePresence, motion } from 'motion/react'
import { useTranslations } from 'next-intl'
import { useForm, useFormState } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'
import { api } from '@api'
import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input
} from '@components'
import { validationSchemas } from '@constants'
import { zodResolver } from '@hookform/resolvers/zod'
import { useAuth } from '@hooks'

const updateEmailSchema = z.object({
  email: validationSchemas.email
})

export const UpdateEmail = () => {
  const { currentUser, updateUserById, isUpdatingUserById } = useAuth()
  const t = useTranslations('Errors')
  const tc = useTranslations('Common')

  const form = useForm({
    resolver: zodResolver(updateEmailSchema),
    defaultValues: {
      email: currentUser?.email ?? ''
    },
    mode: 'onChange'
  })

  const { dirtyFields } = useFormState({
    control: form.control
  })

  const handleSubmit = async (data) => {
    if (data.email === currentUser.email) return

    toast.promise(
      updateUserById({
        id: currentUser.id,
        email: data.email
      }),
      {
        loading: `${tc('updating')}...`,
        success: () => {
          form.reset({ email: data.email })
          api.refreshToken()
          return tc('updatedSuccessfully')
        },
        error: t('updateFailed')
      }
    )
  }

  const showSubmitButton =
    !dirtyFields.email || form.watch('email') === currentUser?.email

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-4'>
        <FormField
          control={form.control}
          name='email'
          render={({ field }) => (
            <FormItem>
              <FormLabel className='text-slate-600'>{tc('email')}</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  type='email'
                  placeholder={tc('email')}
                  disabled={isUpdatingUserById}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <AnimatePresence>
          {!showSubmitButton && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}>
              <Button type='submit' isDisabled={showSubmitButton}>
                {tc('save')}
              </Button>
            </motion.div>
          )}
        </AnimatePresence>
      </form>
    </Form>
  )
}
