'use client'

import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { buttonVariants } from '@components'
import { routes } from '@constants'
import { useAuth } from '@hooks'
import { cn } from '@utils'

export const ChangePassword = () => {
  const t = useTranslations('Common')
  const { currentUser } = useAuth()
  const { email } = currentUser
  return (
    <Link
      href={`${routes.forgotPassword}?email=${email}`}
      className={cn(buttonVariants({ variant: 'default' }), 'text-sm')}>
      {t('changePassword')}
    </Link>
  )
}
