'use client'

import { useContext, useEffect, useState } from 'react'
import { ResumeContext } from '@context'
import { useResume } from '@hooks'

export const ResumeInput = ({ label, field, value = '', inputProps = {} }) => {
  const context = useContext(ResumeContext)

  if (context === undefined) {
    throw new Error('Input must be used within a ResumeContext')
  }

  const [state, setState] = useState(value)
  const { updateResume } = useResume()

  const onChange = ({ target }) => {
    setState(target.value)
    updateResume({ [field]: target.value })
  }

  useEffect(() => {
    setState(value)
  }, [value, setState])

  return (
    <div>
      <label>{label}</label>
      <input
        onChange={onChange}
        value={state}
        {...inputProps}
        className='p-6 border border-slate-500'
      />
    </div>
  )
}
