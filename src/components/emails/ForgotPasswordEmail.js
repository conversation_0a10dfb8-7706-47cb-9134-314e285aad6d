import * as React from 'react'
import { emailTheme, routes } from '@constants'
import {
  Body,
  Button,
  Container,
  Heading,
  Html,
  Link,
  Preview,
  Section,
  Tailwind,
  Text
} from '@react-email/components'
import { EmailFooter } from './EmailFooter'
import { <PERSON><PERSON>Header } from './EmailHeader'

export const ForgotPasswordEmail = ({ token, user }) => {
  const link = `${process.env.NEXT_PUBLIC_URL}${routes.resetPassword}?token=${token}`
  return (
    <Html>
      <Preview>Password reset request</Preview>
      <Tailwind config={emailTheme}>
        <Body className='bg-white my-auto mx-auto font-inter px-2'>
          <Container className='my-10 mx-auto p-5 max-w-[465px]'>
            <EmailHeader />

            <Heading className='text-2xl font-normal text-slate my-8'>
              Password reset request
            </Heading>

            <Text className='text-base text-slate'>Hi,</Text>

            <Text className='text-base text-slate'>
              We received a request to change the password on this account. If you made
              this request, you can reset your password by clicking the following link:
            </Text>

            <Section className='my-8'>
              <Button
                className='bg-primary rounded-md text-white text-base px-6 py-3 font-normal w-full text-center'
                href={link}>
                Reset Password
              </Button>
            </Section>

            <Text className='text-base text-slate'>
              Don&apos;t see a button? you can click this link to reset your password:{' '}
              <Link href={link} className='text-primary no-underline'>
                {link}
              </Link>
            </Text>

            <EmailFooter email={user.email} />
          </Container>
        </Body>
      </Tailwind>
    </Html>
  )
}
