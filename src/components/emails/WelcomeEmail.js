import * as React from 'react'
import { emailTheme, routes } from '@constants'
import {
  Body,
  Button,
  Container,
  Heading,
  Html,
  Preview,
  Section,
  Tailwind,
  Text
} from '@react-email/components'
import { EmailFooter } from './EmailFooter'
import { EmailHeader } from './EmailHeader'

export const WelcomeEmail = ({ firstName, email }) => {
  return (
    <Html>
      <Preview>Welcome to Swift Resume</Preview>
      <Tailwind config={emailTheme}>
        <Body className='bg-white my-auto mx-auto font-inter px-2'>
          <Container className='my-10 mx-auto p-5 max-w-[465px]'>
            <EmailHeader />

            <Heading className='text-2xl font-normal text-slate my-8'>
              Welcome to Swift Resume
            </Heading>

            <Text className='text-base text-slate'>Hi {firstName},</Text>

            <Text className='text-base text-slate'>
              Welcome to the ultimate platform for creating professional resumes!
              We&apos;re your one-stop solution to craft, customize, and build your
              perfect resume in style.
            </Text>

            <Text className='text-base text-slate'>
              So why wait? Select the button below to create your first resume.
            </Text>

            <Section className='my-8'>
              <Button
                className='bg-primary rounded-md text-white text-base px-6 py-3 font-normal w-full text-center'
                href={routes.editor()}>
                Create your first resume
              </Button>
            </Section>

            <EmailFooter email={email} />
          </Container>
        </Body>
      </Tailwind>
    </Html>
  )
}
