import { getFontUrl, isValidFontValue } from '@utils'

export const FontPreloader = ({ selectedFont }) => {
  if (!selectedFont || !isValidFontValue(selectedFont)) {
    return null
  }

  const fontUrl = getFontUrl(selectedFont)

  if (!fontUrl) {
    return null
  }

  return (
    <>
      <link rel='preconnect' href='https://fonts.googleapis.com' />
      <link rel='preconnect' href='https://fonts.gstatic.com' crossOrigin='anonymous' />
      <link
        rel='preload'
        href={fontUrl}
        as='style'
        onLoad={(e) => {
          e.target.onload = null
          e.target.rel = 'stylesheet'
        }}
      />
      <noscript>
        <link rel='stylesheet' href={fontUrl} />
      </noscript>
    </>
  )
}
