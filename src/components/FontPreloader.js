import { getFontUrl, isValidFontValue } from '@utils'

/**
 * FontPreloader component for server-side font preloading
 * Preloads the selected font to ensure it's available before page render
 */
export const FontPreloader = ({ selectedFont }) => {
  // Validate font value and get font URL
  if (!selectedFont || !isValidFontValue(selectedFont)) {
    return null
  }

  const fontUrl = getFontUrl(selectedFont)
  
  if (!fontUrl) {
    return null
  }

  return (
    <>
      {/* Preconnect to Google Fonts for faster loading */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* Preload the selected font */}
      <link
        rel="preload"
        href={fontUrl}
        as="style"
        onLoad="this.onload=null;this.rel='stylesheet'"
      />
      
      {/* Fallback for browsers that don't support onLoad */}
      <noscript>
        <link rel="stylesheet" href={fontUrl} />
      </noscript>
    </>
  )
}
