'use client'

import { useEffect } from 'react'
import { getFontUrl, isValidFontValue } from '@utils'

export const FontPreloader = ({ selectedFont }) => {
  useEffect(() => {
    if (!selectedFont || !isValidFontValue(selectedFont)) {
      return
    }

    const fontUrl = getFontUrl(selectedFont)
    if (!fontUrl) {
      return
    }

    const existingLink = document.querySelector(`link[href="${fontUrl}"]`)
    if (existingLink) {
      return
    }

    if (!document.querySelector('link[href="https://fonts.googleapis.com"]')) {
      const preconnectGoogle = document.createElement('link')
      preconnectGoogle.rel = 'preconnect'
      preconnectGoogle.href = 'https://fonts.googleapis.com'
      document.head.append(preconnectGoogle)
    }

    if (!document.querySelector('link[href="https://fonts.gstatic.com"]')) {
      const preconnectGstatic = document.createElement('link')
      preconnectGstatic.rel = 'preconnect'
      preconnectGstatic.href = 'https://fonts.gstatic.com'
      preconnectGstatic.crossOrigin = 'anonymous'
      document.head.append(preconnectGstatic)
    }

    const fontLink = document.createElement('link')
    fontLink.rel = 'stylesheet'
    fontLink.href = fontUrl
    document.head.append(fontLink)
  }, [selectedFont])

  return null
}
