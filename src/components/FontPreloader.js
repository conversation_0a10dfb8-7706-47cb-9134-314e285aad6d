'use client'

import { useEffect } from 'react'
import { getFontUrl, isValidFontValue } from '@utils'

export const FontPreloader = ({ selectedFont }) => {
  useEffect(() => {
    if (!selectedFont || !isValidFontValue(selectedFont)) {
      return
    }

    const fontUrl = getFontUrl(selectedFont)
    if (!fontUrl) {
      return
    }

    const existingLink = document.querySelector(`link[href="${fontUrl}"]`)
    if (existingLink) {
      return
    }

    const preconnectGoogle = document.createElement('link')
    preconnectGoogle.rel = 'preconnect'
    preconnectGoogle.href = 'https://fonts.googleapis.com'
    document.head.appendChild(preconnectGoogle)

    const preconnectGstatic = document.createElement('link')
    preconnectGstatic.rel = 'preconnect'
    preconnectGstatic.href = 'https://fonts.gstatic.com'
    preconnectGstatic.crossOrigin = 'anonymous'
    document.head.appendChild(preconnectGstatic)

    const fontLink = document.createElement('link')
    fontLink.rel = 'preload'
    fontLink.href = fontUrl
    fontLink.as = 'style'
    fontLink.onload = () => {
      fontLink.onload = null
      fontLink.rel = 'stylesheet'
    }
    document.head.appendChild(fontLink)

    const noscriptLink = document.createElement('noscript')
    noscriptLink.innerHTML = `<link rel="stylesheet" href="${fontUrl}" />`
    document.head.appendChild(noscriptLink)
  }, [selectedFont])

  return null
}
