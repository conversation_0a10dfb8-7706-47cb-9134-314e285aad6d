import * as React from 'react'
import { cva } from 'class-variance-authority'
import { Slot } from '@radix-ui/react-slot'
import { cn } from '@utils'
import { textVariants } from './Text'

export const buttonVariants = cva(
  'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-offset-1 focus-visible:ring-1 focus-visible:ring-slate-950 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 dark:focus-visible:ring-slate-300',
  {
    variants: {
      variant: {
        default:
          'bg-primary text-slate-50 border border-primary-700 shadow-btn-up hover:bg-primary/90 focus:ring-primary dark:bg-slate-50 dark:text-slate-900 dark:hover:bg-slate-50/90',
        secondary:
          'bg-slate-100 text-slate-700 border border-slate-200 shadow-btn-out focus:ring-slate-200 hover:bg-slate-200',
        white:
          'bg-white text-slate-700 border border-slate-200 shadow-btn-flat focus:ring-slate-200 hover:bg-slate-200',
        subtle: 'bg-transparent text-slate-700 focus:ring-slate-700 hover:text-slate-500',
        subtleprimary:
          'bg-transparent text-blue-600 focus:ring-blue-600 hover:text-blue-500',
        icon: 'bg-white text-slate-700 border border-slate-200 shadow-btn-flat hover:bg-slate-200',
        social:
          'bg-white text-slate-700 [&_svg]:size-5 border border-slate-200 shadow-btn-flat hover:bg-slate-200',
        destructive:
          'bg-red-500 text-white border border-red-700 shadow-btn-up hover:bg-red-600 focus:ring-red-600 dark:bg-red-50 dark:text-slate-900 dark:hover:bg-red-50/90'
      },
      size: {
        default: 'px-3 py-1.5',
        link: 'p-0',
        sm: 'text-xs px-1.5 py-1 rounded-md',
        md: 'text-sm leading-4.5 px-3 py-[5.5px]',
        lg: 'px-3.5 py-[7px]',
        xl: 'text-base px-5 py-2 rounded-xl',
        xxl: 'text-base px-5 py-3 rounded-xl'
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'default'
    }
  }
)

export const Button = React.forwardRef(
  (
    {
      className,
      variant,
      size,
      textVariant,
      textSize,
      textWeight,
      asChild = false,
      isDisabled,
      isLoading,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : 'button'
    const disabled = isDisabled || isLoading

    return (
      <Comp
        disabled={disabled}
        className={cn(
          textVariants({ variant: textVariant, size: textSize, weight: textWeight }),
          buttonVariants({ variant, size }),
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)

Button.displayName = 'Button'
