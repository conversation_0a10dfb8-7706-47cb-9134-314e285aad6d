'use client'

import * as React from 'react'
import { cva } from 'class-variance-authority'
import * as LabelPrimitive from '@radix-ui/react-label'
import { cn } from '@utils'
import { textVariants } from './Text'

const labelVariants = cva(
  'text-slate-600 block peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
)

export const Label = React.forwardRef(
  ({ className, variant = 'xs', weight = 'medium', ...props }, ref) => (
    <LabelPrimitive.Root
      ref={ref}
      className={cn(textVariants({ variant, weight }), labelVariants(), className)}
      {...props}
    />
  )
)

Label.displayName = LabelPrimitive.Root.displayName
