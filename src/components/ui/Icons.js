import * as React from 'react'

export const LinkedInIcon = ({ className, ...props }) => (
  <svg
    width='20'
    height='21'
    viewBox='0 0 20 21'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    className={className}
    {...props}>
    <path
      d='M0 1.93266C0 1.14187 0.662031 0.5 1.47812 0.5H18.5219C19.3383 0.5 20 1.14187 20 1.93266V19.0676C20 19.8586 19.3383 20.5 18.5219 20.5H1.47812C0.662109 20.5 0 19.8587 0 19.0678V1.93242V1.93266Z'
      fill='#0A66C2'
    />
    <path
      d='M6.07742 17.2371V8.23305H3.08461V17.2371H6.07773H6.07742ZM4.58164 7.00391C5.62508 7.00391 6.27469 6.3125 6.27469 5.44844C6.25516 4.56469 5.62508 3.89258 4.60148 3.89258C3.57719 3.89258 2.9082 4.56469 2.9082 5.44836C2.9082 6.31242 3.55758 7.00383 4.56203 7.00383H4.58141L4.58164 7.00391ZM7.73398 17.2371H10.7266V12.2094C10.7266 11.9406 10.7461 11.6712 10.8252 11.4792C11.0414 10.9413 11.5338 10.3845 12.3608 10.3845C13.4434 10.3845 13.8768 11.2102 13.8768 12.4207V17.2371H16.8693V12.0745C16.8693 9.30891 15.393 8.02195 13.4241 8.02195C11.8098 8.02195 11.1008 8.92422 10.7069 9.53875H10.7268V8.23336H7.73414C7.7732 9.07805 7.73391 17.2374 7.73391 17.2374L7.73398 17.2371Z'
      fill='white'
    />
  </svg>
)

export const SparkelIcon = ({ className }) => {
  return (
    <svg
      className={className}
      width='16'
      height='16'
      viewBox='0 0 16 16'
      xmlns='http://www.w3.org/2000/svg'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M5 4C5.36246 4 5.67306 4.25922 5.7379 4.61584L5.99021 6.00355C6.08335 6.51578 6.48422 6.91665 6.99645 7.00979L8.38416 7.2621C8.74078 7.32694 9 7.63754 9 8C9 8.36246 8.74078 8.67306 8.38416 8.7379L6.99645 8.99021C6.48422 9.08335 6.08335 9.48422 5.99021 9.99645L5.7379 11.3842C5.67306 11.7408 5.36246 12 5 12C4.63754 12 4.32694 11.7408 4.2621 11.3842L4.00979 9.99645C3.91665 9.48422 3.51578 9.08335 3.00355 8.99021L1.61584 8.7379C1.25922 8.67306 1 8.36246 1 8C1 7.63754 1.25922 7.32694 1.61584 7.2621L3.00355 7.00979C3.51578 6.91665 3.91665 6.51578 4.00979 6.00355L4.2621 4.61584C4.32694 4.25922 4.63754 4 5 4Z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M12 1C12.3349 1 12.6291 1.22198 12.7211 1.54396L12.9159 2.2256C13.0345 2.64086 13.3591 2.96546 13.7744 3.0841L14.456 3.27886C14.778 3.37085 15 3.66514 15 4C15 4.33486 14.778 4.62915 14.456 4.72114L13.7744 4.9159C13.3591 5.03454 13.0345 5.35914 12.9159 5.7744L12.7211 6.45604C12.6291 6.77802 12.3349 7 12 7C11.6651 7 11.3709 6.77802 11.2789 6.45604L11.0841 5.7744C10.9655 5.35914 10.6409 5.03454 10.2256 4.9159L9.54396 4.72114C9.22198 4.62915 9 4.33486 9 4C9 3.66514 9.22198 3.37085 9.54396 3.27886L10.2256 3.0841C10.6409 2.96546 10.9655 2.64086 11.0841 2.2256L11.2789 1.54396C11.3709 1.22198 11.6651 1 12 1Z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10 11C10.3442 11 10.6441 11.2342 10.7276 11.5681C10.8143 11.9149 11.0851 12.1857 11.4319 12.2724C11.7658 12.3559 12 12.6558 12 13C12 13.3442 11.7658 13.6441 11.4319 13.7276C11.0851 13.8143 10.8143 14.0851 10.7276 14.4319C10.6441 14.7658 10.3442 15 10 15C9.65585 15 9.35586 14.7658 9.27239 14.4319C9.18569 14.0851 8.9149 13.8143 8.5681 13.7276C8.23422 13.6441 8 13.3442 8 13C8 12.6558 8.23422 12.3559 8.5681 12.2724C8.9149 12.1857 9.18569 11.9149 9.27239 11.5681C9.35586 11.2342 9.65585 11 10 11Z'
      />
    </svg>
  )
}

export const ResumeSidebarLeft = (props) => {
  return (
    <svg
      width='65'
      height='84'
      viewBox='0 0 65 84'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'>
      <g filter='url(#filter0_d_240_30141)'>
        <path
          d='M2.75 5C2.75 2.79086 4.54086 1 6.75 1H58.75C60.9591 1 62.75 2.79086 62.75 5V77C62.75 79.2091 60.9591 81 58.75 81H6.75C4.54086 81 2.75 79.2091 2.75 77V5Z'
          fill='white'
        />
        <path
          d='M6.75 1.5H58.75C60.683 1.5 62.25 3.067 62.25 5V77C62.25 78.933 60.683 80.5 58.75 80.5H6.75C4.817 80.5 3.25 78.933 3.25 77V5C3.25 3.067 4.817 1.5 6.75 1.5Z'
          stroke='#E2E8F0'
        />
        <rect x='7.75' y='6' width='18' height='70' rx='3' fill='#E2E8F0' />
        <g clipPath='url(#clip0_240_30141)'>
          <rect x='30.75' y='6' width='27' height='2' rx='1' fill='#CBD5E1' />
          <rect x='30.75' y='9' width='17' height='2' rx='1' fill='#E2E8F0' />
          <rect x='30.75' y='15' width='9' height='2' rx='1' fill='#CBD5E1' />
          <g opacity='0.2' clipPath='url(#clip1_240_30141)'>
            <rect x='30.75' y='18' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='21' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='24' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='27' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='30' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='33' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='36' width='29' height='2' rx='1' fill='#94A3B8' />
          </g>
          <rect x='30.75' y='42' width='12' height='2' rx='1' fill='#CBD5E1' />
          <g opacity='0.2' clipPath='url(#clip2_240_30141)'>
            <rect x='30.75' y='45' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='48' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='51' width='27' height='2' rx='1' fill='#94A3B8' />
          </g>
          <rect x='30.75' y='57' width='9' height='2' rx='1' fill='#CBD5E1' />
          <g opacity='0.2' clipPath='url(#clip3_240_30141)'>
            <rect x='30.75' y='60' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='63' width='13' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='69' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='72' width='5' height='2' rx='1' fill='#94A3B8' />
          </g>
        </g>
      </g>
      <defs>
        <filter
          id='filter0_d_240_30141'
          x='0.75'
          y='0'
          width='64'
          height='84'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'>
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_240_30141'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_240_30141'
            result='shape'
          />
        </filter>
        <clipPath id='clip0_240_30141'>
          <rect width='27' height='70' fill='white' transform='translate(30.75 6)' />
        </clipPath>
        <clipPath id='clip1_240_30141'>
          <rect width='27' height='20' fill='white' transform='translate(30.75 18)' />
        </clipPath>
        <clipPath id='clip2_240_30141'>
          <rect width='27' height='8' fill='white' transform='translate(30.75 45)' />
        </clipPath>
        <clipPath id='clip3_240_30141'>
          <rect width='27' height='22' fill='white' transform='translate(30.75 60)' />
        </clipPath>
      </defs>
    </svg>
  )
}

export const ResumeSidebarRight = (props) => {
  return (
    <svg
      width='65'
      height='84'
      viewBox='0 0 65 84'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'>
      <g filter='url(#filter0_d_240_30179)'>
        <path
          d='M2.25 5C2.25 2.79086 4.04086 1 6.25 1H58.25C60.4591 1 62.25 2.79086 62.25 5V77C62.25 79.2091 60.4591 81 58.25 81H6.25C4.04086 81 2.25 79.2091 2.25 77V5Z'
          fill='white'
        />
        <path
          d='M6.25 1.5H58.25C60.183 1.5 61.75 3.067 61.75 5V77C61.75 78.933 60.183 80.5 58.25 80.5H6.25C4.317 80.5 2.75 78.933 2.75 77V5C2.75 3.067 4.317 1.5 6.25 1.5Z'
          stroke='#E2E8F0'
        />
        <g clipPath='url(#clip0_240_30179)'>
          <rect x='7.25' y='6' width='27' height='2' rx='1' fill='#CBD5E1' />
          <rect x='7.25' y='9' width='17' height='2' rx='1' fill='#E2E8F0' />
          <rect x='7.25' y='15' width='9' height='2' rx='1' fill='#CBD5E1' />
          <g opacity='0.2' clipPath='url(#clip1_240_30179)'>
            <rect x='7.25' y='18' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.25' y='21' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.25' y='24' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.25' y='27' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.25' y='30' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.25' y='33' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.25' y='36' width='29' height='2' rx='1' fill='#94A3B8' />
          </g>
          <rect x='7.25' y='42' width='12' height='2' rx='1' fill='#CBD5E1' />
          <g opacity='0.2' clipPath='url(#clip2_240_30179)'>
            <rect x='7.25' y='45' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.25' y='48' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.25' y='51' width='27' height='2' rx='1' fill='#94A3B8' />
          </g>
          <rect x='7.25' y='57' width='9' height='2' rx='1' fill='#CBD5E1' />
          <g opacity='0.2' clipPath='url(#clip3_240_30179)'>
            <rect x='7.25' y='60' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.25' y='63' width='13' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.25' y='69' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.25' y='72' width='5' height='2' rx='1' fill='#94A3B8' />
          </g>
        </g>
        <rect x='39.25' y='6' width='18' height='70' rx='3' fill='#E2E8F0' />
      </g>
      <defs>
        <filter
          id='filter0_d_240_30179'
          x='0.25'
          y='0'
          width='64'
          height='84'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'>
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_240_30179'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_240_30179'
            result='shape'
          />
        </filter>
        <clipPath id='clip0_240_30179'>
          <rect width='27' height='70' fill='white' transform='translate(7.25 6)' />
        </clipPath>
        <clipPath id='clip1_240_30179'>
          <rect width='27' height='20' fill='white' transform='translate(7.25 18)' />
        </clipPath>
        <clipPath id='clip2_240_30179'>
          <rect width='27' height='8' fill='white' transform='translate(7.25 45)' />
        </clipPath>
        <clipPath id='clip3_240_30179'>
          <rect width='27' height='22' fill='white' transform='translate(7.25 60)' />
        </clipPath>
      </defs>
    </svg>
  )
}

export const ResumeFullWidth = (props) => {
  return (
    <svg
      width='65'
      height='84'
      viewBox='0 0 65 84'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'>
      <g filter='url(#filter0_d_240_30217)'>
        <path
          d='M2.75 5C2.75 2.79086 4.54086 1 6.75 1H58.75C60.9591 1 62.75 2.79086 62.75 5V77C62.75 79.2091 60.9591 81 58.75 81H6.75C4.54086 81 2.75 79.2091 2.75 77V5Z'
          fill='white'
        />
        <path
          d='M6.75 1.5H58.75C60.683 1.5 62.25 3.067 62.25 5V77C62.25 78.933 60.683 80.5 58.75 80.5H6.75C4.817 80.5 3.25 78.933 3.25 77V5C3.25 3.067 4.817 1.5 6.75 1.5Z'
          stroke='#E2E8F0'
        />
        <g clipPath='url(#clip0_240_30217)'>
          <path
            d='M7.75 7C7.75 6.44772 8.19772 6 8.75 6H23.75C24.3023 6 24.75 6.44772 24.75 7V7C24.75 7.55228 24.3023 8 23.75 8H8.75C8.19772 8 7.75 7.55228 7.75 7V7Z'
            fill='#CBD5E1'
          />
          <rect x='7.75' y='9' width='28' height='2' rx='1' fill='#E2E8F0' />
          <rect x='7.75' y='15' width='9' height='2' rx='1' fill='#CBD5E1' />
          <g opacity='0.2' clipPath='url(#clip1_240_30217)'>
            <rect x='7.75' y='19' width='50' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.75' y='22' width='50' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.75' y='25' width='50' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.75' y='28' width='50' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.75' y='31' width='50' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.75' y='34' width='50' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.75' y='37' width='29' height='2' rx='1' fill='#94A3B8' />
          </g>
          <rect x='7.75' y='43' width='9' height='2' rx='1' fill='#CBD5E1' />
          <g opacity='0.2' clipPath='url(#clip2_240_30217)'>
            <rect x='7.75' y='47' width='50' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.75' y='50' width='50' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.75' y='53' width='50' height='2' rx='1' fill='#94A3B8' />
          </g>
          <rect x='7.75' y='59' width='9' height='2' rx='1' fill='#CBD5E1' />
          <g opacity='0.2' clipPath='url(#clip3_240_30217)'>
            <rect x='7.75' y='63' width='50' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.75' y='66' width='31' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.75' y='72' width='50' height='2' rx='1' fill='#94A3B8' />
            <rect x='7.75' y='75' width='12' height='2' rx='1' fill='#94A3B8' />
          </g>
        </g>
      </g>
      <defs>
        <filter
          id='filter0_d_240_30217'
          x='0.75'
          y='0'
          width='64'
          height='84'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'>
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_240_30217'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_240_30217'
            result='shape'
          />
        </filter>
        <clipPath id='clip0_240_30217'>
          <rect width='50' height='70' fill='white' transform='translate(7.75 6)' />
        </clipPath>
        <clipPath id='clip1_240_30217'>
          <rect width='50' height='20' fill='white' transform='translate(7.75 19)' />
        </clipPath>
        <clipPath id='clip2_240_30217'>
          <rect width='50' height='8' fill='white' transform='translate(7.75 47)' />
        </clipPath>
        <clipPath id='clip3_240_30217'>
          <rect width='50' height='13' fill='white' transform='translate(7.75 63)' />
        </clipPath>
      </defs>
    </svg>
  )
}

export const ResumeFullSplit = (props) => {
  return (
    <svg
      width='65'
      height='84'
      viewBox='0 0 65 84'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'>
      <g filter='url(#filter0_d_240_30254)'>
        <path
          d='M2.25 5C2.25 2.79086 4.04086 1 6.25 1H58.25C60.4591 1 62.25 2.79086 62.25 5V77C62.25 79.2091 60.4591 81 58.25 81H6.25C4.04086 81 2.25 79.2091 2.25 77V5Z'
          fill='white'
        />
        <path
          d='M6.25 1.5H58.25C60.183 1.5 61.75 3.067 61.75 5V77C61.75 78.933 60.183 80.5 58.25 80.5H6.25C4.317 80.5 2.75 78.933 2.75 77V5C2.75 3.067 4.317 1.5 6.25 1.5Z'
          stroke='#E2E8F0'
        />
        <g clipPath='url(#clip0_240_30254)'>
          <path
            d='M7.25 7C7.25 6.44772 7.69772 6 8.25 6H23.25C23.8023 6 24.25 6.44772 24.25 7V7C24.25 7.55228 23.8023 8 23.25 8H8.25C7.69772 8 7.25 7.55228 7.25 7V7Z'
            fill='#CBD5E1'
          />
          <rect x='7.25' y='9' width='28' height='2' rx='1' fill='#E2E8F0' />
          <rect x='7.25' y='15' width='9' height='2' rx='1' fill='#CBD5E1' />
          <g opacity='0.2' clipPath='url(#clip1_240_30254)'>
            <rect x='18.25' y='15' width='39' height='2' rx='1' fill='#94A3B8' />
            <rect x='18.25' y='18' width='39' height='2' rx='1' fill='#94A3B8' />
            <rect x='18.25' y='21' width='39' height='2' rx='1' fill='#94A3B8' />
            <rect x='18.25' y='24' width='39' height='2' rx='1' fill='#94A3B8' />
            <rect x='18.25' y='27' width='39' height='2' rx='1' fill='#94A3B8' />
            <rect x='18.25' y='30' width='39' height='2' rx='1' fill='#94A3B8' />
            <rect x='18.25' y='33' width='29' height='2' rx='1' fill='#94A3B8' />
          </g>
          <rect x='7.25' y='39' width='9' height='2' rx='1' fill='#CBD5E1' />
          <g opacity='0.2' clipPath='url(#clip2_240_30254)'>
            <rect x='18.25' y='39' width='39' height='2' rx='1' fill='#94A3B8' />
            <rect x='18.25' y='42' width='39' height='2' rx='1' fill='#94A3B8' />
            <rect x='18.25' y='45' width='39' height='2' rx='1' fill='#94A3B8' />
          </g>
          <rect x='7.25' y='51' width='9' height='2' rx='1' fill='#CBD5E1' />
          <g opacity='0.2' clipPath='url(#clip3_240_30254)'>
            <rect x='18.25' y='51' width='39' height='2' rx='1' fill='#94A3B8' />
            <rect x='18.25' y='54' width='31' height='2' rx='1' fill='#94A3B8' />
            <rect x='18.25' y='60' width='39' height='2' rx='1' fill='#94A3B8' />
            <rect x='18.25' y='63' width='12' height='2' rx='1' fill='#94A3B8' />
            <rect x='18.25' y='69' width='31' height='2' rx='1' fill='#94A3B8' />
            <rect x='18.25' y='72' width='31' height='2' rx='1' fill='#94A3B8' />
            <rect x='18.25' y='75' width='5' height='2' rx='1' fill='#94A3B8' />
          </g>
        </g>
      </g>
      <defs>
        <filter
          id='filter0_d_240_30254'
          x='0.25'
          y='0'
          width='64'
          height='84'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'>
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_240_30254'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_240_30254'
            result='shape'
          />
        </filter>
        <clipPath id='clip0_240_30254'>
          <rect width='50' height='70' fill='white' transform='translate(7.25 6)' />
        </clipPath>
        <clipPath id='clip1_240_30254'>
          <rect width='39' height='20' fill='white' transform='translate(18.25 15)' />
        </clipPath>
        <clipPath id='clip2_240_30254'>
          <rect width='39' height='8' fill='white' transform='translate(18.25 39)' />
        </clipPath>
        <clipPath id='clip3_240_30254'>
          <rect width='39' height='25' fill='white' transform='translate(18.25 51)' />
        </clipPath>
      </defs>
    </svg>
  )
}

export const ResumeSidebarNormalStyle = (props) => {
  return (
    <svg
      width='65'
      height='84'
      viewBox='0 0 65 84'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'>
      <g filter='url(#filter0_d_240_30294)'>
        <g clipPath='url(#clip0_240_30294)'>
          <path
            d='M2.25 5C2.25 2.79086 4.04086 1 6.25 1H58.25C60.4591 1 62.25 2.79086 62.25 5V77C62.25 79.2091 60.4591 81 58.25 81H6.25C4.04086 81 2.25 79.2091 2.25 77V5Z'
            fill='white'
          />
          <rect x='2.25' y='1' width='18' height='80' fill='#E2E8F0' />
          <g clipPath='url(#clip1_240_30294)'>
            <rect x='25.25' y='6' width='32' height='2' rx='1' fill='#CBD5E1' />
            <rect x='25.25' y='9' width='17' height='2' rx='1' fill='#E2E8F0' />
            <rect x='25.25' y='15' width='9' height='2' rx='1' fill='#CBD5E1' />
            <g opacity='0.2' clipPath='url(#clip2_240_30294)'>
              <rect x='25.25' y='18' width='32' height='2' rx='1' fill='#94A3B8' />
              <rect x='25.25' y='21' width='32' height='2' rx='1' fill='#94A3B8' />
              <rect x='25.25' y='24' width='32' height='2' rx='1' fill='#94A3B8' />
              <rect x='25.25' y='27' width='32' height='2' rx='1' fill='#94A3B8' />
              <rect x='25.25' y='30' width='32' height='2' rx='1' fill='#94A3B8' />
              <rect x='25.25' y='33' width='32' height='2' rx='1' fill='#94A3B8' />
              <rect x='25.25' y='36' width='29' height='2' rx='1' fill='#94A3B8' />
            </g>
            <rect x='25.25' y='42' width='12' height='2' rx='1' fill='#CBD5E1' />
            <g opacity='0.2' clipPath='url(#clip3_240_30294)'>
              <rect x='25.25' y='45' width='32' height='2' rx='1' fill='#94A3B8' />
              <rect x='25.25' y='48' width='32' height='2' rx='1' fill='#94A3B8' />
              <rect x='25.25' y='51' width='32' height='2' rx='1' fill='#94A3B8' />
            </g>
            <rect x='25.25' y='57' width='9' height='2' rx='1' fill='#CBD5E1' />
            <g opacity='0.2' clipPath='url(#clip4_240_30294)'>
              <rect x='25.25' y='60' width='32' height='2' rx='1' fill='#94A3B8' />
              <rect x='25.25' y='63' width='13' height='2' rx='1' fill='#94A3B8' />
              <rect x='25.25' y='69' width='32' height='2' rx='1' fill='#94A3B8' />
              <rect x='25.25' y='72' width='5' height='2' rx='1' fill='#94A3B8' />
              <rect x='25.25' y='78' width='31' height='2' rx='1' fill='#94A3B8' />
            </g>
          </g>
        </g>
        <path
          d='M6.25 1.5H58.25C60.183 1.5 61.75 3.067 61.75 5V77C61.75 78.933 60.183 80.5 58.25 80.5H6.25C4.317 80.5 2.75 78.933 2.75 77V5C2.75 3.067 4.317 1.5 6.25 1.5Z'
          stroke='#E2E8F0'
        />
      </g>
      <defs>
        <filter
          id='filter0_d_240_30294'
          x='0.25'
          y='0'
          width='64'
          height='84'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'>
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_240_30294'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_240_30294'
            result='shape'
          />
        </filter>
        <clipPath id='clip0_240_30294'>
          <path
            d='M2.25 5C2.25 2.79086 4.04086 1 6.25 1H58.25C60.4591 1 62.25 2.79086 62.25 5V77C62.25 79.2091 60.4591 81 58.25 81H6.25C4.04086 81 2.25 79.2091 2.25 77V5Z'
            fill='white'
          />
        </clipPath>
        <clipPath id='clip1_240_30294'>
          <rect width='42' height='80' fill='white' transform='translate(20.25 1)' />
        </clipPath>
        <clipPath id='clip2_240_30294'>
          <rect width='32' height='20' fill='white' transform='translate(25.25 18)' />
        </clipPath>
        <clipPath id='clip3_240_30294'>
          <rect width='32' height='8' fill='white' transform='translate(25.25 45)' />
        </clipPath>
        <clipPath id='clip4_240_30294'>
          <rect width='32' height='22' fill='white' transform='translate(25.25 60)' />
        </clipPath>
      </defs>
    </svg>
  )
}

export const ResumeSidebarInsetStyle = (props) => {
  return (
    <svg
      width='65'
      height='84'
      viewBox='0 0 65 84'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'>
      <g filter='url(#filter0_d_240_30332)'>
        <path
          d='M2.75 5C2.75 2.79086 4.54086 1 6.75 1H58.75C60.9591 1 62.75 2.79086 62.75 5V77C62.75 79.2091 60.9591 81 58.75 81H6.75C4.54086 81 2.75 79.2091 2.75 77V5Z'
          fill='white'
        />
        <path
          d='M6.75 1.5H58.75C60.683 1.5 62.25 3.067 62.25 5V77C62.25 78.933 60.683 80.5 58.75 80.5H6.75C4.817 80.5 3.25 78.933 3.25 77V5C3.25 3.067 4.817 1.5 6.75 1.5Z'
          stroke='#E2E8F0'
        />
        <rect x='7.75' y='6' width='18' height='70' rx='3' fill='#E2E8F0' />
        <g clipPath='url(#clip0_240_30332)'>
          <rect x='30.75' y='6' width='27' height='2' rx='1' fill='#CBD5E1' />
          <rect x='30.75' y='9' width='17' height='2' rx='1' fill='#E2E8F0' />
          <rect x='30.75' y='15' width='9' height='2' rx='1' fill='#CBD5E1' />
          <g opacity='0.2' clipPath='url(#clip1_240_30332)'>
            <rect x='30.75' y='18' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='21' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='24' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='27' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='30' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='33' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='36' width='29' height='2' rx='1' fill='#94A3B8' />
          </g>
          <rect x='30.75' y='42' width='12' height='2' rx='1' fill='#CBD5E1' />
          <g opacity='0.2' clipPath='url(#clip2_240_30332)'>
            <rect x='30.75' y='45' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='48' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='51' width='27' height='2' rx='1' fill='#94A3B8' />
          </g>
          <rect x='30.75' y='57' width='9' height='2' rx='1' fill='#CBD5E1' />
          <g opacity='0.2' clipPath='url(#clip3_240_30332)'>
            <rect x='30.75' y='60' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='63' width='13' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='69' width='27' height='2' rx='1' fill='#94A3B8' />
            <rect x='30.75' y='72' width='5' height='2' rx='1' fill='#94A3B8' />
          </g>
        </g>
      </g>
      <defs>
        <filter
          id='filter0_d_240_30332'
          x='0.75'
          y='0'
          width='64'
          height='84'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'>
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_240_30332'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_240_30332'
            result='shape'
          />
        </filter>
        <clipPath id='clip0_240_30332'>
          <rect width='27' height='70' fill='white' transform='translate(30.75 6)' />
        </clipPath>
        <clipPath id='clip1_240_30332'>
          <rect width='27' height='20' fill='white' transform='translate(30.75 18)' />
        </clipPath>
        <clipPath id='clip2_240_30332'>
          <rect width='27' height='8' fill='white' transform='translate(30.75 45)' />
        </clipPath>
        <clipPath id='clip3_240_30332'>
          <rect width='27' height='22' fill='white' transform='translate(30.75 60)' />
        </clipPath>
      </defs>
    </svg>
  )
}

export const ResumeSpacingTight = (props) => {
  return (
    <svg
      width='65'
      height='66'
      viewBox='0 0 65 66'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'>
      <g filter='url(#filter0_d_240_30373)'>
        <path
          d='M2.33398 5C2.33398 2.79086 4.12485 1 6.33398 1H58.334C60.5431 1 62.334 2.79086 62.334 5V59C62.334 61.2091 60.5431 63 58.334 63H6.33398C4.12485 63 2.33398 61.2091 2.33398 59V5Z'
          fill='white'
        />
        <path
          d='M6.33398 1.5H58.334C60.267 1.5 61.834 3.067 61.834 5V59C61.834 60.933 60.267 62.5 58.334 62.5H6.33398C4.40099 62.5 2.83398 60.933 2.83398 59V5C2.83398 3.067 4.40099 1.5 6.33398 1.5Z'
          stroke='#E2E8F0'
        />
        <rect x='18.834' y='23.5' width='27' height='3' rx='1.5' fill='#CBD5E1' />
        <rect x='18.834' y='30.5' width='27' height='3' rx='1.5' fill='#CBD5E1' />
        <rect x='18.834' y='37.5' width='27' height='3' rx='1.5' fill='#CBD5E1' />
      </g>
      <defs>
        <filter
          id='filter0_d_240_30373'
          x='0.333984'
          y='0'
          width='64'
          height='66'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'>
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_240_30373'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_240_30373'
            result='shape'
          />
        </filter>
      </defs>
    </svg>
  )
}

export const ResumeSpacingNormal = (props) => {
  return (
    <svg
      width='64'
      height='66'
      viewBox='0 0 64 66'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'>
      <g filter='url(#filter0_d_240_30379)'>
        <path
          d='M2 5C2 2.79086 3.79086 1 6 1H58C60.2091 1 62 2.79086 62 5V59C62 61.2091 60.2091 63 58 63H6C3.79086 63 2 61.2091 2 59V5Z'
          fill='white'
        />
        <path
          d='M6 1.5H58C59.933 1.5 61.5 3.067 61.5 5V59C61.5 60.933 59.933 62.5 58 62.5H6C4.067 62.5 2.5 60.933 2.5 59V5C2.5 3.067 4.067 1.5 6 1.5Z'
          stroke='#E2E8F0'
        />
        <rect x='18.5' y='19.5' width='27' height='3' rx='1.5' fill='#CBD5E1' />
        <rect x='18.5' y='30.5' width='27' height='3' rx='1.5' fill='#CBD5E1' />
        <rect x='18.5' y='41.5' width='27' height='3' rx='1.5' fill='#CBD5E1' />
      </g>
      <defs>
        <filter
          id='filter0_d_240_30379'
          x='0'
          y='0'
          width='64'
          height='66'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'>
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_240_30379'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_240_30379'
            result='shape'
          />
        </filter>
      </defs>
    </svg>
  )
}

export const ResumeSpacingLoose = (props) => {
  return (
    <svg
      width='65'
      height='66'
      viewBox='0 0 65 66'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'>
      <g filter='url(#filter0_d_240_30385)'>
        <path
          d='M2.66797 5C2.66797 2.79086 4.45883 1 6.66797 1H58.668C60.8771 1 62.668 2.79086 62.668 5V59C62.668 61.2091 60.8771 63 58.668 63H6.66797C4.45883 63 2.66797 61.2091 2.66797 59V5Z'
          fill='white'
        />
        <path
          d='M6.66797 1.5H58.668C60.601 1.5 62.168 3.067 62.168 5V59C62.168 60.933 60.601 62.5 58.668 62.5H6.66797C4.73497 62.5 3.16797 60.933 3.16797 59V5C3.16797 3.067 4.73497 1.5 6.66797 1.5Z'
          stroke='#E2E8F0'
        />
        <rect x='19.168' y='15.5' width='27' height='3' rx='1.5' fill='#CBD5E1' />
        <rect x='19.168' y='30.5' width='27' height='3' rx='1.5' fill='#CBD5E1' />
        <rect x='19.168' y='45.5' width='27' height='3' rx='1.5' fill='#CBD5E1' />
      </g>
      <defs>
        <filter
          id='filter0_d_240_30385'
          x='0.667969'
          y='0'
          width='64'
          height='66'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'>
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_240_30385'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_240_30385'
            result='shape'
          />
        </filter>
      </defs>
    </svg>
  )
}

export const ResumeAccentsRibbon = (props) => {
  return (
    <svg
      width='65'
      height='84'
      viewBox='0 0 65 84'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'>
      <g filter='url(#filter0_d_240_30394)'>
        <g clipPath='url(#clip0_240_30394)'>
          <path
            d='M2.83398 5C2.83398 2.79086 4.62485 1 6.83398 1H58.834C61.0431 1 62.834 2.79086 62.834 5V77C62.834 79.2091 61.0431 81 58.834 81H6.83398C4.62485 81 2.83398 79.2091 2.83398 77V5Z'
            fill='white'
          />
          <g clipPath='url(#clip1_240_30394)'>
            <path
              d='M7.83398 9C7.83398 8.44772 8.2817 8 8.83398 8H23.834C24.3863 8 24.834 8.44772 24.834 9V9C24.834 9.55228 24.3863 10 23.834 10H8.83398C8.2817 10 7.83398 9.55228 7.83398 9V9Z'
              fill='#CBD5E1'
            />
            <rect x='7.83398' y='11' width='28' height='2' rx='1' fill='#E2E8F0' />
            <rect x='7.83398' y='17' width='9' height='2' rx='1' fill='#CBD5E1' />
            <g opacity='0.2' clipPath='url(#clip2_240_30394)'>
              <rect x='7.83398' y='21' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.83398' y='24' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.83398' y='27' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.83398' y='30' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.83398' y='33' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.83398' y='36' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.83398' y='39' width='29' height='2' rx='1' fill='#94A3B8' />
            </g>
            <rect x='7.83398' y='45' width='9' height='2' rx='1' fill='#CBD5E1' />
            <g opacity='0.2' clipPath='url(#clip3_240_30394)'>
              <rect x='7.83398' y='49' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.83398' y='52' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.83398' y='55' width='50' height='2' rx='1' fill='#94A3B8' />
            </g>
            <rect x='7.83398' y='61' width='9' height='2' rx='1' fill='#CBD5E1' />
            <g opacity='0.2' clipPath='url(#clip4_240_30394)'>
              <rect x='7.83398' y='65' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.83398' y='68' width='31' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.83398' y='74' width='50' height='2' rx='1' fill='#94A3B8' />
            </g>
          </g>
          <line
            x1='2.83398'
            y1='3.5'
            x2='62.834'
            y2='3.5'
            stroke='#2563EB'
            strokeWidth='3'
          />
        </g>
        <path
          d='M6.83398 1.5H58.834C60.767 1.5 62.334 3.067 62.334 5V77C62.334 78.933 60.767 80.5 58.834 80.5H6.83398C4.90099 80.5 3.33398 78.933 3.33398 77V5C3.33398 3.067 4.90099 1.5 6.83398 1.5Z'
          stroke='#E2E8F0'
        />
      </g>
      <defs>
        <filter
          id='filter0_d_240_30394'
          x='0.833984'
          y='0'
          width='64'
          height='84'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'>
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_240_30394'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_240_30394'
            result='shape'
          />
        </filter>
        <clipPath id='clip0_240_30394'>
          <path
            d='M2.83398 5C2.83398 2.79086 4.62485 1 6.83398 1H58.834C61.0431 1 62.834 2.79086 62.834 5V77C62.834 79.2091 61.0431 81 58.834 81H6.83398C4.62485 81 2.83398 79.2091 2.83398 77V5Z'
            fill='white'
          />
        </clipPath>
        <clipPath id='clip1_240_30394'>
          <rect width='50' height='68' fill='white' transform='translate(7.83398 8)' />
        </clipPath>
        <clipPath id='clip2_240_30394'>
          <rect width='50' height='20' fill='white' transform='translate(7.83398 21)' />
        </clipPath>
        <clipPath id='clip3_240_30394'>
          <rect width='50' height='8' fill='white' transform='translate(7.83398 49)' />
        </clipPath>
        <clipPath id='clip4_240_30394'>
          <rect width='50' height='11' fill='white' transform='translate(7.83398 65)' />
        </clipPath>
      </defs>
    </svg>
  )
}

export const ResumeAccentsBlur = (props) => {
  return (
    <svg
      width='65'
      height='84'
      viewBox='0 0 65 84'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'>
      <g filter='url(#filter0_d_240_30432)'>
        <g clipPath='url(#clip0_240_30432)'>
          <path
            d='M2.5 5C2.5 2.79086 4.29086 1 6.5 1H58.5C60.7091 1 62.5 2.79086 62.5 5V77C62.5 79.2091 60.7091 81 58.5 81H6.5C4.29086 81 2.5 79.2091 2.5 77V5Z'
            fill='white'
          />
          <g opacity='0.72' filter='url(#filter1_f_240_30432)'>
            <circle
              cx='49.0174'
              cy='19.1756'
              r='7.81073'
              transform='rotate(180 49.0174 19.1756)'
              fill='#FAC1D2'
            />
            <circle
              cx='39.1072'
              cy='12.6248'
              r='7.81073'
              transform='rotate(180 39.1072 12.6248)'
              fill='#D2EFFC'
            />
            <circle
              cx='49.6893'
              cy='8.08966'
              r='7.81073'
              transform='rotate(180 49.6893 8.08966)'
              fill='#E2D7FF'
            />
          </g>
          <g clipPath='url(#clip1_240_30432)'>
            <path
              d='M7.5 7C7.5 6.44772 7.94772 6 8.5 6H23.5C24.0523 6 24.5 6.44772 24.5 7V7C24.5 7.55228 24.0523 8 23.5 8H8.5C7.94772 8 7.5 7.55228 7.5 7V7Z'
              fill='#CBD5E1'
            />
            <rect x='7.5' y='9' width='28' height='2' rx='1' fill='#E2E8F0' />
            <rect x='7.5' y='15' width='9' height='2' rx='1' fill='#CBD5E1' />
            <g opacity='0.2' clipPath='url(#clip2_240_30432)'>
              <rect x='7.5' y='19' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.5' y='22' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.5' y='25' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.5' y='28' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.5' y='31' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.5' y='34' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.5' y='37' width='29' height='2' rx='1' fill='#94A3B8' />
            </g>
            <rect x='7.5' y='43' width='9' height='2' rx='1' fill='#CBD5E1' />
            <g opacity='0.2' clipPath='url(#clip3_240_30432)'>
              <rect x='7.5' y='47' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.5' y='50' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.5' y='53' width='50' height='2' rx='1' fill='#94A3B8' />
            </g>
            <rect x='7.5' y='59' width='9' height='2' rx='1' fill='#CBD5E1' />
            <g opacity='0.2' clipPath='url(#clip4_240_30432)'>
              <rect x='7.5' y='63' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.5' y='66' width='31' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.5' y='72' width='50' height='2' rx='1' fill='#94A3B8' />
              <rect x='7.5' y='75' width='12' height='2' rx='1' fill='#94A3B8' />
            </g>
          </g>
        </g>
        <path
          d='M6.5 1.5H58.5C60.433 1.5 62 3.067 62 5V77C62 78.933 60.433 80.5 58.5 80.5H6.5C4.567 80.5 3 78.933 3 77V5C3 3.067 4.567 1.5 6.5 1.5Z'
          stroke='#E2E8F0'
        />
      </g>
      <defs>
        <filter
          id='filter0_d_240_30432'
          x='0.5'
          y='0'
          width='64'
          height='84'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'>
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_240_30432'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_240_30432'
            result='shape'
          />
        </filter>
        <filter
          id='filter1_f_240_30432'
          x='9.03376'
          y='-22.1752'
          width='63.6551'
          height='64.3504'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'>
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='BackgroundImageFix'
            result='shape'
          />
          <feGaussianBlur
            stdDeviation='7.59445'
            result='effect1_foregroundBlur_240_30432'
          />
        </filter>
        <clipPath id='clip0_240_30432'>
          <path
            d='M2.5 5C2.5 2.79086 4.29086 1 6.5 1H58.5C60.7091 1 62.5 2.79086 62.5 5V77C62.5 79.2091 60.7091 81 58.5 81H6.5C4.29086 81 2.5 79.2091 2.5 77V5Z'
            fill='white'
          />
        </clipPath>
        <clipPath id='clip1_240_30432'>
          <rect width='50' height='70' fill='white' transform='translate(7.5 6)' />
        </clipPath>
        <clipPath id='clip2_240_30432'>
          <rect width='50' height='20' fill='white' transform='translate(7.5 19)' />
        </clipPath>
        <clipPath id='clip3_240_30432'>
          <rect width='50' height='8' fill='white' transform='translate(7.5 47)' />
        </clipPath>
        <clipPath id='clip4_240_30432'>
          <rect width='50' height='13' fill='white' transform='translate(7.5 63)' />
        </clipPath>
      </defs>
    </svg>
  )
}

export const ResumeAccentsHeader = () => {
  return <></>
}

export const ResumeAccentsNone = () => {
  return (
    <svg
      width='64'
      height='84'
      viewBox='0 0 64 84'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'>
      <g filter='url(#filter0_d_330_26820)'>
        <path
          d='M2 5C2 2.79086 3.79086 1 6 1H58C60.2091 1 62 2.79086 62 5V77C62 79.2091 60.2091 81 58 81H6C3.79086 81 2 79.2091 2 77V5Z'
          fill='white'
        />
        <path
          d='M6 1.5H58C59.933 1.5 61.5 3.067 61.5 5V77C61.5 78.933 59.933 80.5 58 80.5H6C4.067 80.5 2.5 78.933 2.5 77V5C2.5 3.067 4.067 1.5 6 1.5Z'
          stroke='#E2E8F0'
        />
      </g>
      <defs>
        <filter
          id='filter0_d_330_26820'
          x='0'
          y='0'
          width='64'
          height='84'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'>
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_330_26820'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_330_26820'
            result='shape'
          />
        </filter>
      </defs>
    </svg>
  )
}

export const ResumeHeaderDefault = (props) => {
  return (
    <svg
      width='65'
      height='66'
      viewBox='0 0 65 66'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'>
      <g filter='url(#filter0_d_240_30576)'>
        <path
          d='M2.75 5C2.75 2.79086 4.54086 1 6.75 1H58.75C60.9591 1 62.75 2.79086 62.75 5V59C62.75 61.2091 60.9591 63 58.75 63H6.75C4.54086 63 2.75 61.2091 2.75 59V5Z'
          fill='white'
        />
        <path
          d='M6.75 1.5H58.75C60.683 1.5 62.25 3.067 62.25 5V59C62.25 60.933 60.683 62.5 58.75 62.5H6.75C4.817 62.5 3.25 60.933 3.25 59V5C3.25 3.067 4.817 1.5 6.75 1.5Z'
          stroke='#E2E8F0'
        />
        <path
          d='M40.0156 24.0781L40.168 28.3789H39.418C39.2461 27.4883 39.0078 26.8125 38.7031 26.3516C38.4062 25.8828 37.9648 25.5586 37.3789 25.3789C36.793 25.1914 35.9844 25.0781 34.9531 25.0391L34.2852 25.0039V38.5625C34.2852 38.9375 34.332 39.2344 34.4258 39.4531C34.5195 39.6641 34.707 39.8242 34.9883 39.9336C35.2773 40.043 35.7188 40.1328 36.3125 40.2031V41H29.1758V40.2031C29.7695 40.1328 30.207 40.043 30.4883 39.9336C30.7695 39.8242 30.957 39.6641 31.0508 39.4531C31.1445 39.2344 31.1914 38.9375 31.1914 38.5625V25.0039L30.5352 25.0391C29.5117 25.0781 28.7031 25.1914 28.1094 25.3789C27.5234 25.5586 27.0781 25.8789 26.7734 26.3398C26.4766 26.8008 26.2422 27.4805 26.0703 28.3789H25.3203L25.4727 24.0781H40.0156Z'
          fill='black'
        />
      </g>
      <defs>
        <filter
          id='filter0_d_240_30576'
          x='0.75'
          y='0'
          width='64'
          height='66'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'>
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_240_30576'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_240_30576'
            result='shape'
          />
        </filter>
      </defs>
    </svg>
  )
}

export const ResumeHeaderUnderline = (props) => {
  return (
    <svg
      width='65'
      height='66'
      viewBox='0 0 65 66'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'>
      <g filter='url(#filter0_d_240_30580)'>
        <path
          d='M2.25 5C2.25 2.79086 4.04086 1 6.25 1H58.25C60.4591 1 62.25 2.79086 62.25 5V59C62.25 61.2091 60.4591 63 58.25 63H6.25C4.04086 63 2.25 61.2091 2.25 59V5Z'
          fill='white'
        />
        <path
          d='M6.25 1.5H58.25C60.183 1.5 61.75 3.067 61.75 5V59C61.75 60.933 60.183 62.5 58.25 62.5H6.25C4.317 62.5 2.75 60.933 2.75 59V5C2.75 3.067 4.317 1.5 6.25 1.5Z'
          stroke='#E2E8F0'
        />
        <path
          d='M39.5156 24.0781L39.668 28.3789H38.918C38.7461 27.4883 38.5078 26.8125 38.2031 26.3516C37.9062 25.8828 37.4648 25.5586 36.8789 25.3789C36.293 25.1914 35.4844 25.0781 34.4531 25.0391L33.7852 25.0039V38.5625C33.7852 38.9375 33.832 39.2344 33.9258 39.4531C34.0195 39.6641 34.207 39.8242 34.4883 39.9336C34.7773 40.043 35.2188 40.1328 35.8125 40.2031V41H28.6758V40.2031C29.2695 40.1328 29.707 40.043 29.9883 39.9336C30.2695 39.8242 30.457 39.6641 30.5508 39.4531C30.6445 39.2344 30.6914 38.9375 30.6914 38.5625V25.0039L30.0352 25.0391C29.0117 25.0781 28.2031 25.1914 27.6094 25.3789C27.0234 25.5586 26.5781 25.8789 26.2734 26.3398C25.9766 26.8008 25.7422 27.4805 25.5703 28.3789H24.8203L24.9727 24.0781H39.5156Z'
          fill='black'
        />
        <path d='M24.2578 44.12H40.2305V45.92H24.2578V44.12Z' fill='black' />
      </g>
      <defs>
        <filter
          id='filter0_d_240_30580'
          x='0.25'
          y='0'
          width='64'
          height='66'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'>
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_240_30580'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_240_30580'
            result='shape'
          />
        </filter>
      </defs>
    </svg>
  )
}

export const ResumeHeaderDotted = (props) => {
  return (
    <svg
      width='65'
      height='66'
      viewBox='0 0 65 66'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'>
      <g filter='url(#filter0_d_240_30584)'>
        <path
          d='M2.75 5C2.75 2.79086 4.54086 1 6.75 1H58.75C60.9591 1 62.75 2.79086 62.75 5V59C62.75 61.2091 60.9591 63 58.75 63H6.75C4.54086 63 2.75 61.2091 2.75 59V5Z'
          fill='white'
        />
        <path
          d='M6.75 1.5H58.75C60.683 1.5 62.25 3.067 62.25 5V59C62.25 60.933 60.683 62.5 58.75 62.5H6.75C4.817 62.5 3.25 60.933 3.25 59V5C3.25 3.067 4.817 1.5 6.75 1.5Z'
          stroke='#E2E8F0'
        />
        <path
          d='M40.0156 24.0781L40.168 28.3789H39.418C39.2461 27.4883 39.0078 26.8125 38.7031 26.3516C38.4062 25.8828 37.9648 25.5586 37.3789 25.3789C36.793 25.1914 35.9844 25.0781 34.9531 25.0391L34.2852 25.0039V38.5625C34.2852 38.9375 34.332 39.2344 34.4258 39.4531C34.5195 39.6641 34.707 39.8242 34.9883 39.9336C35.2773 40.043 35.7188 40.1328 36.3125 40.2031V41H29.1758V40.2031C29.7695 40.1328 30.207 40.043 30.4883 39.9336C30.7695 39.8242 30.957 39.6641 31.0508 39.4531C31.1445 39.2344 31.1914 38.9375 31.1914 38.5625V25.0039L30.5352 25.0391C29.5117 25.0781 28.7031 25.1914 28.1094 25.3789C27.5234 25.5586 27.0781 25.8789 26.7734 26.3398C26.4766 26.8008 26.2422 27.4805 26.0703 28.3789H25.3203L25.4727 24.0781H40.0156Z'
          fill='black'
        />
        <path
          d='M30.15 45.02C30.15 45.5171 29.7471 45.92 29.25 45.92C28.7529 45.92 28.35 45.5171 28.35 45.02C28.35 44.5229 28.7529 44.12 29.25 44.12C29.7471 44.12 30.15 44.5229 30.15 45.02ZM33.75 45.02C33.75 45.5171 33.3471 45.92 32.85 45.92C32.3529 45.92 31.95 45.5171 31.95 45.02C31.95 44.5229 32.3529 44.12 32.85 44.12C33.3471 44.12 33.75 44.5229 33.75 45.02ZM37.35 45.02C37.35 45.5171 36.9471 45.92 36.45 45.92C35.953 45.92 35.55 45.5171 35.55 45.02C35.55 44.5229 35.953 44.12 36.45 44.12C36.9471 44.12 37.35 44.5229 37.35 45.02Z'
          fill='black'
        />
      </g>
      <defs>
        <filter
          id='filter0_d_240_30584'
          x='0.75'
          y='0'
          width='64'
          height='66'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'>
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_240_30584'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_240_30584'
            result='shape'
          />
        </filter>
      </defs>
    </svg>
  )
}

export const ResumeHeaderSquiggle = (props) => {
  return (
    <svg
      width='65'
      height='66'
      viewBox='0 0 65 66'
      fill='none'
      {...props}
      xmlns='http://www.w3.org/2000/svg'>
      <g filter='url(#filter0_d_240_30588)'>
        <path
          d='M2.25 5C2.25 2.79086 4.04086 1 6.25 1H58.25C60.4591 1 62.25 2.79086 62.25 5V59C62.25 61.2091 60.4591 63 58.25 63H6.25C4.04086 63 2.25 61.2091 2.25 59V5Z'
          fill='white'
        />
        <path
          d='M6.25 1.5H58.25C60.183 1.5 61.75 3.067 61.75 5V59C61.75 60.933 60.183 62.5 58.25 62.5H6.25C4.317 62.5 2.75 60.933 2.75 59V5C2.75 3.067 4.317 1.5 6.25 1.5Z'
          stroke='#E2E8F0'
        />
        <path
          d='M39.5156 24.0781L39.668 28.3789H38.918C38.7461 27.4883 38.5078 26.8125 38.2031 26.3516C37.9062 25.8828 37.4648 25.5586 36.8789 25.3789C36.293 25.1914 35.4844 25.0781 34.4531 25.0391L33.7852 25.0039V38.5625C33.7852 38.9375 33.832 39.2344 33.9258 39.4531C34.0195 39.6641 34.207 39.8242 34.4883 39.9336C34.7773 40.043 35.2188 40.1328 35.8125 40.2031V41H28.6758V40.2031C29.2695 40.1328 29.707 40.043 29.9883 39.9336C30.2695 39.8242 30.457 39.6641 30.5508 39.4531C30.6445 39.2344 30.6914 38.9375 30.6914 38.5625V25.0039L30.0352 25.0391C29.0117 25.0781 28.2031 25.1914 27.6094 25.3789C27.0234 25.5586 26.5781 25.8789 26.2734 26.3398C25.9766 26.8008 25.7422 27.4805 25.5703 28.3789H24.8203L24.9727 24.0781H39.5156Z'
          fill='black'
        />
        <path
          d='M24.2578 45.32C23.9264 45.32 23.6578 45.5886 23.6578 45.92C23.6578 46.2514 23.9264 46.52 24.2578 46.52V45.32ZM24.2578 46.52C25.3418 46.52 25.8969 45.8579 26.2844 45.4151C26.6844 44.9579 26.9168 44.72 27.4078 44.72V43.52C26.3239 43.52 25.7687 44.1821 25.3813 44.6249C24.9812 45.0821 24.7489 45.32 24.2578 45.32V46.52Z'
          fill='black'
        />
        <path
          d='M27.4078 44.72C27.8989 44.72 28.1312 44.9579 28.5313 45.4151C28.9187 45.8579 29.4739 46.52 30.5578 46.52V45.32C30.0668 45.32 29.8344 45.0821 29.4344 44.6249C29.0469 44.1821 28.4918 43.52 27.4078 43.52V44.72Z'
          fill='black'
        />
        <path
          d='M30.5578 46.52C31.6418 46.52 32.1969 45.8579 32.5844 45.4151C32.9844 44.9579 33.2168 44.72 33.7078 44.72V43.52C32.6239 43.52 32.0687 44.1821 31.6813 44.6249C31.2812 45.0821 31.0489 45.32 30.5578 45.32V46.52Z'
          fill='black'
        />
        <path
          d='M33.7078 44.72C34.1989 44.72 34.4312 44.9579 34.8313 45.4151C35.2187 45.8579 35.7739 46.52 36.8578 46.52V45.32C36.3668 45.32 36.1344 45.0821 35.7344 44.6249C35.3469 44.1821 34.7918 43.52 33.7078 43.52V44.72Z'
          fill='black'
        />
        <path
          d='M36.8578 46.52C37.9418 46.52 38.497 45.8579 38.8844 45.4151C39.2845 44.9579 39.5168 44.72 40.0078 44.72V43.52C38.9239 43.52 38.3687 44.1821 37.9813 44.6249C37.5812 45.0821 37.3489 45.32 36.8578 45.32V46.52ZM40.0078 44.72C40.3392 44.72 40.6078 44.4514 40.6078 44.12C40.6078 43.7886 40.3392 43.52 40.0078 43.52V44.72Z'
          fill='black'
        />
      </g>
      <defs>
        <filter
          id='filter0_d_240_30588'
          x='0.25'
          y='0'
          width='64'
          height='66'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'>
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_240_30588'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_240_30588'
            result='shape'
          />
        </filter>
      </defs>
    </svg>
  )
}
