export const SwiftLogo = ({ className = '' }) => {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='151'
      height='33'
      fill='none'
      viewBox='0 0 151 33'
      className={className}>
      <g filter='url(#a)'>
        <mask
          id='c'
          width='21'
          height='24'
          x='5'
          y='3'
          maskUnits='userSpaceOnUse'
          style={{ maskType: 'alpha' }}>
          <path
            fill='url(#b)'
            d='M7.747 6.512a3.07 3.07 0 0 1 3.028-2.574H22.37a3.068 3.068 0 0 1 3.028 3.561L22.73 23.863a3.07 3.07 0 0 1-3.028 2.575H8.109a3.068 3.068 0 0 1-3.029-3.562z'></path>
        </mask>
        <g mask='url(#c)'>
          <path
            fill='url(#d)'
            stroke='#fff'
            strokeWidth='1.125'
            d='M13.732 4.5h4.547c1.335 0 2.3 0 3.05.065.745.065 1.22.19 1.589.4a3.53 3.53 0 0 1 1.552 1.827c.15.399.196.886.14 1.632-.057.751-.212 1.704-.426 3.021l-1.534 9.41c-.16.983-.275 1.69-.41 2.244-.132.547-.273.904-.465 1.194a3.53 3.53 0 0 1-1.495 1.271c-.317.143-.693.225-1.254.268-.567.043-1.284.043-2.28.043h-4.547c-1.335 0-2.3 0-3.05-.065-.746-.065-1.22-.19-1.59-.4a3.53 3.53 0 0 1-1.552-1.826c-.15-.4-.196-.887-.14-1.633.057-.751.212-1.704.426-3.021l1.534-9.41c.16-.983.276-1.69.41-2.244.132-.547.273-.904.465-1.194a3.53 3.53 0 0 1 1.495-1.271c.318-.143.693-.225 1.254-.268.568-.043 1.284-.043 2.28-.043Z'></path>
        </g>
      </g>
      <g style={{ mixBlendMode: 'hard-light' }}>
        <mask
          id='f'
          width='21'
          height='23'
          x='2'
          y='1'
          maskUnits='userSpaceOnUse'
          style={{ maskType: 'alpha' }}>
          <path
            fill='url(#e)'
            d='M4.987 3.7a3.07 3.07 0 0 1 3.028-2.575h11.593a3.068 3.068 0 0 1 3.028 3.562L19.97 21.05a3.07 3.07 0 0 1-3.028 2.575H5.348a3.068 3.068 0 0 1-3.028-3.562z'></path>
        </mask>
        <g mask='url(#f)'>
          <path
            fill='url(#g)'
            stroke='#fff'
            strokeWidth='1.125'
            d='M10.971 1.688h4.547c1.335 0 2.3 0 3.05.065.746.064 1.22.189 1.59.4a3.53 3.53 0 0 1 1.551 1.826c.15.399.196.887.14 1.633-.056.75-.211 1.703-.426 3.02l-1.533 9.41c-.16.984-.276 1.69-.41 2.244-.132.547-.274.904-.465 1.194a3.53 3.53 0 0 1-1.496 1.272c-.317.143-.692.225-1.253.267-.568.043-1.285.044-2.281.044H9.438c-1.335 0-2.3-.001-3.05-.066-.746-.064-1.22-.189-1.59-.4a3.53 3.53 0 0 1-1.552-1.826c-.149-.399-.196-.887-.14-1.633.057-.75.212-1.703.427-3.02l1.533-9.41c.16-.984.276-1.69.41-2.244.132-.547.273-.904.465-1.194a3.53 3.53 0 0 1 1.496-1.272c.317-.143.692-.225 1.253-.267.568-.043 1.284-.044 2.281-.044Z'></path>
        </g>
      </g>
      <path
        fill='#000'
        d='M32.2 22.286q-1.428 0-2.527-.429-1.088-.428-1.713-1.241-.627-.824-.627-1.989v-.132q0-.066.011-.143h3.175q-.087 1.616 1.78 1.616.912 0 1.571-.396.67-.406.67-1.066 0-.681-1.208-.933l-1.967-.385q-3.043-.615-3.043-2.966 0-1.22.714-2.132.713-.922 1.934-1.428 1.22-.505 2.757-.505 2.088 0 3.34.9 1.253.89 1.253 2.44 0 .274-.044.582h-3q.088-.759-.362-1.187-.45-.44-1.264-.44-.9 0-1.494.44-.582.429-.582 1.033 0 .692 1.12.934l2.132.428q3.032.615 3.032 2.78 0 1.252-.758 2.208-.747.945-2.032 1.483-1.285.528-2.868.528M40.593 22l-1.648-11.602h3.351l.385 4.054q.087 1.034.154 2.12.066 1.088.12 2.165.45-1.078.89-2.164.45-1.088.89-2.12l1.79-4.055h2.912l.429 4.054q.099 1.022.187 2.11.087 1.087.164 2.164.418-1.076.824-2.153.418-1.088.846-2.12l1.736-4.055h3.472L51.602 22H48.25l-.582-4.614a72 72 0 0 1-.165-1.494l-.132-1.55q-.33.78-.66 1.55-.328.768-.67 1.494L43.923 22zm15.583 0L58.1 10.398h3.24L59.418 22zm3.79-13.162q-.746 0-1.186-.505t-.319-1.23q.121-.737.726-1.242a2.07 2.07 0 0 1 1.362-.505q.758 0 1.197.505t.319 1.242q-.12.724-.736 1.23a2.07 2.07 0 0 1-1.362.505m10.057 1.56-.417 2.538h-2.384L65.53 23.1q-.286 1.757-1.296 2.625t-3.01.868h-1.725l.428-2.614h.956q.724 0 1.044-.297.33-.286.45-1l1.615-9.745h-2.01l.417-2.538h2.01l.253-1.505q.539-3.263 4.307-3.263h1.846l-.429 2.538h-1.11q-.692 0-1.021.297-.33.285-.45.978l-.155.955zm7.25 0-.417 2.538h-2.373l-.901 5.483q-.099.593.099.823.208.22.856.22h1.242L75.362 22h-1.88q-1.921 0-2.702-.747t-.516-2.33l.989-5.987h-2.01l.417-2.538h2.01l.528-3.164h3.23l-.528 3.164zM80.737 22V10.398h1.78v1.824h.044q.318-.9 1.01-1.45.693-.55 1.78-.55a4.5 4.5 0 0 1 .791.067v1.867a4 4 0 0 0-.428-.077 4 4 0 0 0-.715-.055q-.966 0-1.68.682-.715.68-.715 2.153V22zm11.315.264q-1.703 0-2.912-.78-1.197-.78-1.834-2.143-.637-1.372-.637-3.12 0-1.768.67-3.142t1.856-2.153q1.187-.791 2.725-.791 1.615 0 2.802.769 1.186.769 1.823 2.142.648 1.374.648 3.175v.517h-8.679q.066 1.703.978 2.768.912 1.066 2.56 1.066 1.24 0 2.021-.572a2.72 2.72 0 0 0 1.055-1.472h1.868a4.4 4.4 0 0 1-.912 1.956 4.8 4.8 0 0 1-1.736 1.307q-1.033.473-2.296.473m-3.516-7.13h6.79q-.154-1.527-1.066-2.428t-2.34-.901q-1.427 0-2.33.9-.9.901-1.054 2.428m14.326 7.13q-1.242 0-2.253-.407a3.8 3.8 0 0 1-1.637-1.23q-.625-.835-.703-2.099h1.846q.198 2.132 2.703 2.132 1.208 0 1.9-.506.703-.505.703-1.274 0-.66-.483-1.055-.483-.396-1.263-.571l-1.868-.44q-3.274-.768-3.274-3.295 0-.99.527-1.747.539-.77 1.483-1.198.957-.44 2.198-.44 1.208 0 2.164.462.968.45 1.527 1.286.571.835.604 1.988h-1.823a2 2 0 0 0-.682-1.527q-.67-.604-1.79-.604-1.066 0-1.703.516t-.637 1.264q0 .659.483 1.065.483.396 1.318.594l1.868.439q1.67.396 2.45 1.186.78.78.78 2.033 0 1.077-.582 1.846-.582.768-1.593 1.175-1 .407-2.263.407m10.392-.044a4.6 4.6 0 0 1-2.143-.506q-.966-.516-1.549-1.538-.571-1.032-.571-2.57v-7.208h1.868v7.076q0 1.504.725 2.274.725.758 2.021.758 1.275 0 2.165-.78t.889-2.538v-6.79h1.868V22h-1.824v-1.703q-.603.99-1.483 1.461-.868.462-1.966.462m7.535-.22V10.398h1.824v1.88q.549-1.023 1.461-1.561a3.8 3.8 0 0 1 1.978-.538q1.186 0 2.076.604t1.253 1.483q.472-.89 1.417-1.483.945-.604 2.241-.604 1.066 0 1.934.461.879.462 1.395 1.395.527.934.527 2.363V22h-1.867v-7.602q0-1.351-.758-1.923a2.8 2.8 0 0 0-1.758-.582q-1.275 0-2.011.78-.725.768-.725 1.944V22h-1.867v-7.8q0-1.044-.704-1.67-.692-.637-1.746-.637-.726 0-1.374.34a2.6 2.6 0 0 0-1.032 1.022q-.396.67-.396 1.67V22zm23.159.264q-1.703 0-2.912-.78-1.197-.78-1.834-2.143-.638-1.372-.638-3.12 0-1.768.671-3.142.67-1.373 1.856-2.153 1.187-.791 2.725-.791 1.615 0 2.801.769t1.824 2.142q.648 1.374.648 3.175v.517h-8.679q.066 1.703.978 2.768.912 1.066 2.56 1.066 1.24 0 2.021-.572.791-.57 1.055-1.472h1.868a4.4 4.4 0 0 1-.912 1.956 4.8 4.8 0 0 1-1.736 1.307q-1.033.473-2.296.473m-3.516-7.13h6.79q-.154-1.527-1.066-2.428t-2.34-.901q-1.429 0-2.329.9-.901.901-1.055 2.428'></path>
      <defs>
        <linearGradient
          id='b'
          x1='7.643'
          x2='23.37'
          y1='13.58'
          y2='15.631'
          gradientUnits='userSpaceOnUse'>
          <stop></stop>
          <stop offset='1' stopColor='#737373'></stop>
        </linearGradient>
        <linearGradient
          id='d'
          x1='15.239'
          x2='15.239'
          y1='3.938'
          y2='26.438'
          gradientUnits='userSpaceOnUse'>
          <stop stopColor='#DEF1FF'></stop>
          <stop offset='1' stopColor='#2E61F4'></stop>
        </linearGradient>
        <linearGradient
          id='e'
          x1='4.882'
          x2='20.609'
          y1='10.768'
          y2='12.818'
          gradientUnits='userSpaceOnUse'>
          <stop></stop>
          <stop offset='1' stopColor='#737373'></stop>
        </linearGradient>
        <linearGradient
          id='g'
          x1='12.478'
          x2='12.478'
          y1='1.125'
          y2='23.625'
          gradientUnits='userSpaceOnUse'>
          <stop stopColor='#816BFF'></stop>
          <stop offset='1' stopColor='#4200FF'></stop>
        </linearGradient>
        <filter
          id='a'
          width='26.663'
          height='29.25'
          x='3.032'
          y='2.813'
          colorInterpolationFilters='sRGB'
          filterUnits='userSpaceOnUse'>
          <feFlood floodOpacity='0' result='BackgroundImageFix'></feFlood>
          <feColorMatrix
            in='SourceAlpha'
            result='hardAlpha'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'></feColorMatrix>
          <feOffset dx='1.125' dy='2.25'></feOffset>
          <feGaussianBlur stdDeviation='1.688'></feGaussianBlur>
          <feComposite in2='hardAlpha' operator='out'></feComposite>
          <feColorMatrix values='0 0 0 0 0.25098 0 0 0 0 0.439216 0 0 0 0 0.964706 0 0 0 0.16 0'></feColorMatrix>
          <feBlend
            in2='BackgroundImageFix'
            result='effect1_dropShadow_3571_11209'></feBlend>
          <feBlend
            in='SourceGraphic'
            in2='effect1_dropShadow_3571_11209'
            result='shape'></feBlend>
        </filter>
      </defs>
    </svg>
  )
}
