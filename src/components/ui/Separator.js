'use client'

import * as React from 'react'
import * as SeparatorPrimitive from '@radix-ui/react-separator'
import { Text } from '@components'
import { cn } from '@utils'

const Separator = React.forwardRef(
  ({ className, orientation = 'horizontal', decorative = true, ...props }, ref) => (
    <SeparatorPrimitive.Root
      ref={ref}
      decorative={decorative}
      orientation={orientation}
      className={cn(
        'shrink-0 bg-slate-200 dark:bg-slate-800',
        orientation === 'horizontal' ? 'h-px w-full' : 'h-full w-[1px]',
        className
      )}
      {...props}
    />
  )
)
Separator.displayName = SeparatorPrimitive.Root.displayName

const SeparatorWithText = ({ text, className }) => {
  return (
    <div className={cn('relative my-2', className)}>
      <Separator orientation='horizontal' />
      <div className='absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 flex justify-center'>
        <Text
          as='span'
          variant='xs'
          weight='medium'
          className='text-slate-400 bg-white px-1.5 text-nowrap'>
          {text}
        </Text>
      </div>
    </div>
  )
}

export { Separator, SeparatorWithText }
