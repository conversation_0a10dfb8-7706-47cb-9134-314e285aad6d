'use client'

import * as React from 'react'
import { useTranslations } from 'next-intl'
import { Controller, FormProvider, useFormContext } from 'react-hook-form'
import { Slot } from '@radix-ui/react-slot'
import { cn, getErrorParams } from '@utils'
import { Label } from './Label'
import { Text } from './Text'

const FormFieldContext = React.createContext({})

export const FormField = ({ ...props }) => {
  return (
    <FormFieldContext.Provider value={{ name: props.name }}>
      <Controller {...props} />
    </FormFieldContext.Provider>
  )
}

export const useFormField = () => {
  const fieldContext = React.useContext(FormFieldContext)
  const itemContext = React.useContext(FormItemContext)
  const { getFieldState, formState } = useFormContext()

  const fieldState = getFieldState(fieldContext.name, formState)

  if (!fieldContext) {
    throw new Error('useForm<PERSON>ield should be used within <FormField>')
  }

  const { id } = itemContext

  return {
    id,
    name: fieldContext.name,
    formItemId: `${id}-form-item`,
    formDescriptionId: `${id}-form-item-description`,
    formMessageId: `${id}-form-item-message`,
    ...fieldState
  }
}

const FormItemContext = React.createContext({})

export const FormItem = React.forwardRef(({ className, ...props }, ref) => {
  const id = React.useId()

  return (
    <FormItemContext.Provider value={{ id }}>
      <div ref={ref} className={cn('space-y-1.5', className)} {...props} />
    </FormItemContext.Provider>
  )
})
FormItem.displayName = 'FormItem'

export const FormLabel = React.forwardRef(({ className, ...props }, ref) => {
  const { formItemId } = useFormField()

  return <Label ref={ref} className={className} htmlFor={formItemId} {...props} />
})
FormLabel.displayName = 'FormLabel'

export const FormControl = React.forwardRef(({ ...props }, ref) => {
  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()

  return (
    <Slot
      ref={ref}
      id={formItemId}
      aria-describedby={
        error ? `${formDescriptionId} ${formMessageId}` : `${formDescriptionId}`
      }
      aria-invalid={!!error}
      {...props}
    />
  )
})
FormControl.displayName = 'FormControl'

export const FormDescription = React.forwardRef(({ className, ...props }, ref) => {
  const { formDescriptionId } = useFormField()

  return (
    <p
      ref={ref}
      id={formDescriptionId}
      className={cn('text-[0.8rem] text-slate-500 dark:text-slate-400', className)}
      {...props}
    />
  )
})
FormDescription.displayName = 'FormDescription'

export const FormMessage = React.forwardRef(({ className, children, ...props }, ref) => {
  const { error, formMessageId } = useFormField()
  const t = useTranslations('Errors')

  const body = error?.message ? t(error.message, getErrorParams(error.message)) : children

  if (!body) {
    return null
  }

  return (
    <Text
      as='p'
      ref={ref}
      id={formMessageId}
      variant='xs'
      weight='medium'
      className={cn('text-red-500 dark:text-red-900', className)}
      {...props}>
      {body}
    </Text>
  )
})
FormMessage.displayName = 'FormMessage'

export const Form = FormProvider

Form.displayName = 'Form'
