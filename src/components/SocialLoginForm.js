'use client'

import { useActionState } from 'react'
import { Loader2 } from 'lucide-react'
import { usePathname, useSearchParams } from 'next/navigation'
import { initiateOAuthFlow } from '@actions/users'
import { Button, LinkedInIcon } from '@components'
import { auth, routes } from '@constants'

const initialState = {
  error: false,
  url: null
}

export function SocialLoginForm({ provider, className, label }) {
  const searchParams = useSearchParams()
  const pathname = usePathname()
  const [, formAction, isPending] = useActionState(initiateOAuthFlow, initialState)
  const redirectTo = searchParams.get('successUrl') || routes.dashboard

  let Icon = null

  switch (provider) {
    case auth.social.providers.LINKEDIN: {
      Icon = <LinkedInIcon className='h-5 w-5 mr-2' />
      break
    }
    default: {
      Icon = null
    }
  }

  return (
    <form action={formAction} className={className}>
      <input type='hidden' name='provider' value={provider} />
      <input type='hidden' name='successUrl' value={redirectTo} />
      <input type='hidden' name='errorUrl' value={pathname} />
      <Button
        type='submit'
        variant='social'
        size='lg'
        className='w-full'
        disabled={isPending}>
        <>
          {isPending ? <Loader2 className='animate-spin' /> : Icon}
          {label}
        </>
      </Button>
    </form>
  )
}
