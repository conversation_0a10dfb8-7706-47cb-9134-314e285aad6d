'use client'

import { useTranslations } from 'next-intl'
import { Label, RadioGroup, RadioGroupItem, Text } from '@components'
import { cn, colorPickerGradient, resumeOptions } from '@utils'

export const ColorPicker = ({ value, onChange }) => {
  const options = resumeOptions.palette.options

  return (
    <RadioGroup
      className='flex items-center gap-3 flex-wrap lg:flex-nowrap'
      value={value}
      onValueChange={onChange}>
      {options.map((option, index) => {
        return (
          <div key={index} className='flex items-center'>
            <RadioGroupItem value={option.value} id={option.value} className='sr-only' />
            <Label
              htmlFor={option.value}
              className={cn(
                'cursor-pointer size-8 rounded-full border-none transition hover:ring-2 hover:ring-offset-2 hover:ring-primary',
                value === option.value &&
                  'ring-2 ring-offset-2 ring-primary focus:ring-2 focus:ring-offset-2 focus:ring-primary'
              )}
              style={{
                background: colorPickerGradient[option.value]
              }}
            />
          </div>
        )
      })}
    </RadioGroup>
  )
}

export const ColorPickerLabel = ({ label }) => {
  const tc = useTranslations('Common')
  return (
    <Text as='span' variant='sm' weight='medium' className='block text-slate-700 mb-3'>
      {tc(label)}
    </Text>
  )
}
