'use server'

import { revalidatePath } from 'next/cache'
import { api } from '@api'
import { resumes, routes } from '@constants'
import { SwiftError } from '@error'

export const deleteResumeById = async (id) => {
  const { data, error } = await api.deleteResumeById({ path: { id } })
  if (error) {
    throw new SwiftError(resumes.errors.deleteError)
  }
  revalidatePath(routes.dashboard)
  return data.doc.id
}
