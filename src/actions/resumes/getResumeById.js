'use server'

import { isAnon } from '@access'
import { api } from '@api'
import { payload, resumes } from '@constants'
import { getCurrentUser } from '@context'
import { SwiftError } from '@error'
import { matchPayloadErrorMessage } from '@utils'

export const getResumeById = async (id) => {
  console.log('getResumeById', id)

  const user = await getCurrentUser()

  if (!user) {
    console.error('No user when getting resume by ID')
    throw new SwiftError(resumes.errors.getError)
  }

  let resumeId = id

  if (isAnon(user.role)) {
    const resumes = user.resumes?.docs

    if (!resumes?.length) {
      throw new SwiftError(resumes.errors.notFound)
    }

    // Anon users should only ever have 1 resume
    resumeId = user.resumes.docs[0]
  }

  const { data, error } = await api.findResumeById({
    path: {
      id: resumeId
    }
  })

  if (error) {
    const notFound = matchPayloadErrorMessage(error, payload.notFound)

    if (notFound) {
      throw new SwiftError(resumes.errors.notFound)
    }

    console.error('Error getting resume', error)
    throw new SwiftError(resumes.errors.getError)
  }

  console.log('Got resume', data)
  return data
}
