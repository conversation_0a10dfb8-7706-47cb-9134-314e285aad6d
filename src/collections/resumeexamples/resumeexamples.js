import { isAdminOrEditor, isAnyone } from '@collections/access'
import { collections } from '@constants'
import { resumeFields } from '@utils'

export const resumeExamples = {
  slug: collections.resumeExamples.slug,
  access: {
    create: isAdminOrEditor,
    read: isAnyone,
    update: isAdminOrEditor,
    delete: isAdminOrEditor
  },
  timestamps: true,
  admin: {
    defaultColumns: ['jobTitle', 'firstName', 'lastName', 'user'],
    useAsTitle: 'jobTitle'
  },
  fields: resumeFields
}
