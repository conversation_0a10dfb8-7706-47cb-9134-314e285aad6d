import { isAdminOrSelf, isAnyone, isLoggedIn } from '@collections/access'
import { collections } from '@constants'

export const media = {
  slug: collections.media.slug,
  access: {
    create: isLoggedIn,
    read: isAnyone,
    update: isAdminOrSelf,
    delete: isAdminOrSelf
  },
  admin: {
    useAsTitle: 'filename',
    description: 'Media files uploaded to the system'
  },
  upload: {
    mimeTypes: collections.media.fileTypes,
    imageSizes: [
      {
        name: 'thumbnail',
        width: 300
      }
    ],
    disableLocalStorage: true,
    limits: {
      fileSize: collections.media.maxFileSize
    },
    adminThumbnail: ({ doc }) => {
      if (doc.sizes.thumbnail?.filename) {
        return `${process.env.DO_S3_ENDPOINT}/${process.env.DO_S3_BUCKET}/${collections.media.uploadDir}/${doc.sizes.thumbnail.filename}`
      }
      return null
    }
  },
  timestamps: true,
  fields: [
    {
      name: 'alt',
      type: 'text'
    },
    {
      name: 'uploaded<PERSON>y',
      type: 'relationship',
      label: 'Uploaded by',
      relationTo: [collections.admins.slug, collections.users.slug],
      hasMany: false,
      admin: {
        position: 'sidebar'
      }
    }
  ]
}
