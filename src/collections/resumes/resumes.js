import { isAdmin, isLoggedIn } from '@collections/access'
import { collections } from '@constants'
import { resumeFields } from '@utils'
import { oneResumePerAnonUser } from './hooks'

const canAccessResume = ({ req }) => {
  if (!isLoggedIn({ req })) {
    return false
  }

  if (isAdmin({ req })) {
    return true
  }

  return {
    user: {
      equals: req.user.id
    }
  }
}

export const resumes = {
  slug: collections.resumes.slug,
  access: {
    create: isLoggedIn,
    read: canAccessResume,
    update: canAccessResume,
    delete: canAccessResume
  },
  auth: false,
  timestamps: true,
  admin: {
    defaultColumns: ['jobTitle', 'firstName', 'lastName', 'user'],
    useAsTitle: 'jobTitle'
  },
  hooks: {
    beforeChange: [oneResumePerAnonUser]
  },
  fields: resumeFields
}
