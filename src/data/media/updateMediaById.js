//import { api } from '@api'
import { collections, media, validationSchemas } from '@constants'
import { SwiftError } from '@error'

const { errors } = media

export const updateMediaById = async (id, file, currentUserId) => {
  const validation = validationSchemas.file.safeParse(file)
  if (!validation.success) {
    const errorMessage = validation.error.issues[0].message
    throw new SwiftError(errorMessage)
  }

  // Create FormData instance
  const formData = new FormData()
  formData.append('file', file)
  const payload = {
    prefix: collections.media.uploadDir,
    uploadedBy: { relationTo: collections.users.slug, value: currentUserId }
  }
  formData.append('_payload', JSON.stringify(payload))

  //TODO: Uncomment this when the API is ready, currently openapi is not supporting file upload
  // const { data, error } = await api.updateMedia({
  //   body: formData,
  //   path: { id },
  //   headers: {
  //     'Content-Type': 'multipart/form-data'
  //   }
  // })

  try {
    const response = await fetch(process.env.NEXT_PUBLIC_URL + '/api/media/' + id, {
      method: 'PATCH',
      body: formData
    })

    const data = await response.json()

    return data.doc
  } catch (error) {
    console.log('Error updating media:', error)
    throw new SwiftError(errors.uploadError)
  }
}
