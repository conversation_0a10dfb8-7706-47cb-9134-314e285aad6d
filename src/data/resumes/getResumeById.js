import { api } from '@api'
import { payload, resumes } from '@constants'
import { SwiftError } from '@error'
import { matchPayloadErrorMessage } from '@utils'

export const getResumeById = async (id) => {
  const { data, error } = await api.findResumeById({
    path: {
      id: id
    }
  })

  if (error) {
    const notFound = matchPayloadErrorMessage(error, payload.notFound)

    if (notFound) {
      throw new SwiftError(resumes.errors.notFound)
    }

    console.error('Error getting resume', error)
    throw new SwiftError(resumes.errors.getError)
  }

  console.log('Got resume', data)
  return data
}
