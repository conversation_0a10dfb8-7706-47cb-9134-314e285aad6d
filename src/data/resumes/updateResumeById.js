import { api } from '@api'
import { resumes } from '@constants'
import { SwiftError } from '@error'
import { excludeKeys } from '@utils'

const excludedKeys = ['id', 'user', 'updatedAt', 'createdAt']

export const updateResumeById = async ({ id, update }) => {
  console.log('updateResumeById', { id, update })

  const cleanedData = excludeKeys(update, excludedKeys)

  const { data, error } = await api.updateResumeById({
    path: { id },
    body: cleanedData
  })

  if (error) {
    console.error('Error updating resume', error)
    throw new SwiftError(resumes.errors.updateError)
  }

  console.log('Updated resume', data)

  return data
}
