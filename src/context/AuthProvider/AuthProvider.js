'use client'

import { createContext, useEffect, useMemo, useState } from 'react'
import { getCookie, setCookie } from 'tiny-cookie'
import { usePathname, useRouter } from 'next/navigation'
import { isAnon } from '@access'
import { createAnonUser, createUser } from '@actions/users'
import { Loading } from '@components'
import { auth, routes } from '@constants'
import { updateUserById } from '@data'
import { useMutation, useQuery } from '@tanstack/react-query'
import { isDev } from '@utils'
import { authenticate } from './authenticate'
import { getCurrentUser } from './getCurrentUser'
import { signOut } from './signOut'

export const AuthContext = createContext()

export const AuthProvider = ({ children, authType }) => {
  const [currentUser, setCurrentUser] = useState()
  const router = useRouter()
  const pathname = usePathname()

  const getCurrentUserQuery = useQuery({
    queryKey: ['getCurrentUser'],
    queryFn: getCurrentUser,
    staleTime: 0
  })

  const updateUserMutation = useMutation({
    mutationFn: updateUserById,
    onSuccess: () => {
      getCurrentUserQuery.refetch()
    }
  })

  const createUserMutation = useMutation({
    mutationFn: createUser,
    onSuccess: () => {
      setCookie(auth.hasAccountCookie, true, { secure: true })
    }
  })

  const createAnonUserMutation = useMutation({
    mutationFn: async () => {
      const email = await createAnonUser()
      await authenticate({ email, password: auth.anonUserPassword })
      await getCurrentUserQuery.refetch()
    }
  })

  const authenticateMutation = useMutation({
    mutationFn: async ({ credentials }) => {
      await authenticate(credentials)
      await getCurrentUserQuery.refetch()
    }
  })

  const signOutMutation = useMutation({
    mutationFn: async () => {
      await signOut()
      await getCurrentUserQuery.refetch()
    }
  })

  useEffect(() => {
    setCurrentUser(getCurrentUserQuery.data)
  }, [getCurrentUserQuery.data])

  const value = useMemo(
    () => ({
      currentUser,

      authenticate: authenticateMutation.mutate,
      isAuthenticating: authenticateMutation.isPending,
      authenticateError: authenticateMutation.error,

      signOut: signOutMutation.mutate,
      isSigningOut: signOutMutation.isPending,
      signOutError: signOutMutation.error,

      createUser: createUserMutation.mutate,
      isCreatingUser: createUserMutation.isPending,
      createUserError: createUserMutation.error,

      updateUserById: updateUserMutation.mutateAsync,
      isUpdatingUserById: updateUserMutation.isPending,
      updateUserByIdError: updateUserMutation.error
    }),
    [
      currentUser,

      authenticateMutation.mutate,
      authenticateMutation.isPending,
      authenticateMutation.error,

      signOutMutation.mutate,
      signOutMutation.isPending,
      signOutMutation.error,

      createUserMutation.mutate,
      createUserMutation.isPending,
      createUserMutation.error,

      updateUserMutation.mutateAsync,
      updateUserMutation.isPending,
      updateUserMutation.error
    ]
  )

  const isPublicRoute = authType === auth.types.public
  const isAuthenticatedRoute = authType === auth.types.authenticated
  const isAnonEnabledRoute = authType === auth.types.anonEnabled

  const isGettingCurrentUser = getCurrentUserQuery.isLoading
  const isCreatingAnonUser = createAnonUserMutation.isPending
  const isAuthenticating = authenticateMutation.isPending

  const isProcessing = isGettingCurrentUser || isCreatingAnonUser || isAuthenticating

  const isLoggedOut = currentUser === null
  const currentUserNotSet = currentUser === undefined
  const isPasswordResetFlow =
    pathname === routes.forgotPassword || pathname === routes.resetPassword

  const log = { currentUser, getCurrentUserQuery, createAnonUserMutation }

  const handlePublicRoute = () => {
    isDev && console.log('handlePublicRoute', log)

    if (currentUser && !isPasswordResetFlow) {
      router.push(routes.dashboard)
    }
  }

  const handleAuthenticatedRoute = () => {
    isDev && console.log('handleAuthenticatedRoute', log)

    if (isLoggedOut) {
      router.push(routes.signIn)
      return
    }

    if (isAnon(currentUser.role)) {
      console.log(
        'TODO: Anon user accessing authenticated route. We should re-route to a popup or just redirect to their one resume.'
      )
    }
  }

  const handleAnonEnabledRoute = async () => {
    isDev && console.log('handleAnonEnabledRoute', log)

    if (isLoggedOut && !getCurrentUserQuery.data) {
      const hasAccount = getCookie(auth.hasAccountCookie)

      if (hasAccount) {
        router.push(routes.signIn)
        return
      }

      await createAnonUserMutation.mutateAsync()
    }
  }

  const routeHandlers = {
    [auth.types.public]: handlePublicRoute,
    [auth.types.authenticated]: handleAuthenticatedRoute,
    [auth.types.anonEnabled]: handleAnonEnabledRoute
  }

  const handler = routeHandlers[authType]

  useEffect(() => {
    if (isDev) {
      window.state = window.state || {}
      window.state.useAuth = value
    }

    if (currentUserNotSet || isProcessing) {
      return
    }

    handler()
  }, [currentUserNotSet, isProcessing, handler, value])

  const isLoading =
    (isAuthenticatedRoute && (currentUserNotSet || isLoggedOut)) ||
    (isPublicRoute && (currentUserNotSet || (currentUser && !isPasswordResetFlow))) ||
    (isAnonEnabledRoute && (currentUserNotSet || isLoggedOut))

  if (isLoading) {
    return <Loading />
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
