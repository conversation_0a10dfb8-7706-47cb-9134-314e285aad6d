'use client'

import { createContext, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useRouter } from 'next/navigation'
import { isAnon, isMember } from '@access'
import { createResume } from '@actions'
import { Loading } from '@components'
import { editor, resumes, routes } from '@constants'
import { getResumeById, updateResumeById } from '@data'
import { useAuth, useUndoRedo } from '@hooks'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { isDev } from '@utils'

export const ResumeContext = createContext()

const sourceUndoRedo = 'undoRedo'

export const ResumeProvider = ({ children, id }) => {
  const router = useRouter()
  const { currentUser } = useAuth()
  const { undoStack, redoStack, pushToUndoStack, undo, redo } = useUndoRedo()

  const [isSaving, setIsSaving] = useState(false)

  const queryClient = useQueryClient()
  const getResumeKey = ['getResume', id]

  const getResumeQuery = useQuery({
    queryKey: getResumeKey,
    queryFn: () => getResumeById(id)
  })

  const useDebouncedMutation = (options) => {
    const { mutate, ...mutation } = useMutation(options)
    const timer = useRef()

    const debouncedMutate = async (variables) => {
      clearTimeout(timer.current)

      await queryClient.cancelQueries({ queryKey: getResumeKey, exact: true })

      const previousResume = queryClient.getQueryData(getResumeKey)

      const { update, source } = variables

      queryClient.setQueryData(getResumeKey, (currentResume) => {
        if (id === currentResume?.id) {
          return { ...currentResume, ...update }
        }
      })

      if (source !== sourceUndoRedo) {
        pushToUndoStack(previousResume)
      }

      timer.current = setTimeout(() => {
        mutate({ id, previousResume, ...variables }, options)
      }, editor.updateDatabaseDelayMs)
    }

    return { debouncedMutate, ...mutation }
  }

  const updateResumeMutation = useDebouncedMutation({
    mutationFn: updateResumeById,
    onError: (error, variables) => {
      console.error('Error updating resume', { error, variables })
      queryClient.setQueryData(getResumeKey, variables.previousResume)
    },
    onSettled: () => {
      setIsSaving(false)
      queryClient.invalidateQueries({ queryKey: getResumeKey, exact: true })
    }
  })

  const createResumeMutation = useMutation({
    mutationFn: createResume
  })

  const saveTimer = useRef()

  const updateResume = useCallback(
    (update, source = '') => {
      clearTimeout(saveTimer.current)

      // Adding a small delay before telling the user we are saving
      // makes the experience feel a bit nicer
      saveTimer.current = setTimeout(() => {
        setIsSaving(true)
      }, editor.savingTimerDelay)

      updateResumeMutation.debouncedMutate({ update, source })
    },
    [updateResumeMutation]
  )

  const resume = getResumeQuery.data

  const handleUndo = useCallback(() => {
    const previousState = undo(resume)

    if (previousState) {
      updateResume(previousState, sourceUndoRedo)
    }
  }, [undo, resume, updateResume])

  const handleRedo = useCallback(() => {
    const nextState = redo(resume)

    if (nextState) {
      updateResume(nextState, sourceUndoRedo)
    }
  }, [redo, resume, updateResume])

  const createResumeMutate = createResumeMutation.mutate
  const isGettingResume = getResumeQuery.isLoading
  const getResumeError = getResumeQuery.error
  const isUpdatingResume = updateResumeMutation.isPending
  const updateResumeError = updateResumeMutation.error

  const value = useMemo(
    () => ({
      resume,
      isGettingResume,
      getResumeError,
      updateResume,
      isUpdatingResume,
      updateResumeError,
      undo: handleUndo,
      redo: handleRedo,
      undoStack,
      redoStack,
      isSaving
    }),
    [
      resume,
      isGettingResume,
      getResumeError,
      updateResume,
      isUpdatingResume,
      updateResumeError,
      handleUndo,
      handleRedo,
      undoStack,
      redoStack,
      isSaving
    ]
  )

  useEffect(() => {
    if (isDev) {
      window.state = window.state || {}
      window.state.useResume = value
    }

    async function init() {
      const resumeNotFound =
        getResumeError && getResumeError.message === resumes.errors.notFound.digest

      if (resumeNotFound && isMember(currentUser.role)) {
        router.push(routes.dashboard)
      }

      if (resumeNotFound && isAnon(currentUser.role)) {
        createResumeMutate(null, {
          onSuccess: (resume) => {
            router.push(routes.editor(resume.id))
          },
          onError: (error) => {
            console.error('Failed to create resume', error)
          }
        })
      }

      if (!resume) {
        return
      }

      if (resume.id !== id) {
        router.push(routes.editor(resume.id))
      }
    }
    init()
  }, [getResumeError, currentUser, createResumeMutate, router, id, resume, value])

  if (isGettingResume || getResumeError) {
    return <Loading />
  }

  return <ResumeContext.Provider value={value}>{children}</ResumeContext.Provider>
}
