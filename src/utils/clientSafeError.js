export const clientSafeError = (error) => {
  const isError = error instanceof Error

  if (!isError) {
    console.warn('You must pass an error object to clientSafeError. Instead got:', error)
    return error
  }

  const clientError = new Error(error.message || '')

  for (const entry of Object.entries(error)) {
    const [name, value] = entry
    clientError[name] = value
  }

  // Remove stack trace and any other sensitive data that shouldn't go to the client
  delete clientError.stack

  clientError.name = error.name
  clientError.message = error.message
  clientError.digest = error.digest

  return clientError
}
