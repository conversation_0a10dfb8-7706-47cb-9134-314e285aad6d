const extractErrorMessages = (error) => {
  if (!error || !error.errors) {
    return []
  }

  return error.errors.flatMap((error) => {
    // Collect the top-level message
    const messages = error.message ? [error.message] : []

    // Collect nested messages, if present
    if (error.data?.errors?.length) {
      messages.push(...error.data.errors.map((error) => error.message))
    }

    return messages
  })
}

export const matchPayloadErrorMessage = (error, message) => {
  const errors = extractErrorMessages(error)
  return errors.includes(message)
}
