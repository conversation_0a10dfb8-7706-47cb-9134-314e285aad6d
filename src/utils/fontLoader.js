import { resumeOptions } from './resumeConstant'

/**
 * Get font configuration object by font value
 * @param {string} fontValue - The font value (e.g., 'timeless', 'retro')
 * @returns {Object|undefined} Font configuration object
 */
export const getFontConfig = (fontValue) => {
  return resumeOptions.font.options.find(font => font.value === fontValue)
}

/**
 * Get font family CSS string by font value
 * @param {string} fontValue - The font value (e.g., 'timeless', 'retro')
 * @returns {string} Font family CSS string
 */
export const getFontFamily = (fontValue) => {
  const config = getFontConfig(fontValue)
  return config?.fontFamily || '"Inter", sans-serif'
}

/**
 * Get Google Fonts URL by font value
 * @param {string} fontValue - The font value (e.g., 'timeless', 'retro')
 * @returns {string|undefined} Google Fonts URL
 */
export const getFontUrl = (fontValue) => {
  const config = getFontConfig(fontValue)
  return config?.url
}

/**
 * Get all available font values
 * @returns {string[]} Array of font values
 */
export const getAllFontValues = () => {
  return resumeOptions.font.options.map(font => font.value)
}

/**
 * Check if a font value is valid
 * @param {string} fontValue - The font value to validate
 * @returns {boolean} True if valid, false otherwise
 */
export const isValidFontValue = (fontValue) => {
  return resumeOptions.font.options.some(font => font.value === fontValue)
}
