import { resumeOptions } from './resumeConstant'

export const getFontConfig = (fontValue) => {
  return resumeOptions.font.options.find((font) => font.value === fontValue)
}

export const getFontFamily = (fontValue) => {
  const config = getFontConfig(fontValue)
  return config?.fontFamily || '"Inter", sans-serif'
}

export const getFontUrl = (fontValue) => {
  const config = getFontConfig(fontValue)
  return config?.url
}

export const isValidFontValue = (fontValue) => {
  return resumeOptions.font.options.some((font) => font.value === fontValue)
}
