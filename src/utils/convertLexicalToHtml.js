export const convertLexicalToHtml = (description) => {
  if (!description) return ''

  if (typeof description === 'string') {
    return description
  }

  const editorState = description

  if (!editorState?.root?.children) {
    return ''
  }

  const style = `margin-bottom: 0.5rem;`

  const convertNode = (node) => {
    if (!node) return ''

    switch (node.type) {
      case 'paragraph': {
        const paragraphContent =
          node.children?.map((child) => convertNode(child)).join('') || ''
        return paragraphContent ? `<p style="${style}">${paragraphContent}</p>` : ''
      }

      case 'text': {
        let text = node.text || ''
        if (node.format) {
          if (node.format & 1) text = `<strong style="font-weight: 700;">${text}</strong>`
          if (node.format & 2) text = `<em>${text}</em>`
          if (node.format & 4) text = `<s>${text}</s>`
          if (node.format & 8) text = `<u>${text}</u>`
        }
        return text
      }

      case 'link': {
        const linkContent =
          node.children?.map((child) => convertNode(child)).join('') || ''
        return `<a ${style} href="${node.url || '#'}">${linkContent}</a>`
      }

      case 'list': {
        const listItems = node.children?.map((child) => convertNode(child)).join('') || ''
        const listTag = node.listType === 'number' ? 'ol' : 'ul'
        const listStyle =
          listTag === 'ul'
            ? 'list-style: disc; padding-left: 1rem;'
            : 'list-style: decimal; padding-left: 1rem;'
        return `<${listTag} style="${listStyle} ${style}">${listItems}</${listTag}>`
      }

      case 'listitem': {
        const itemContent =
          node.children?.map((child) => convertNode(child)).join('') || ''
        const itemStyle = `margin-bottom: 5px;`
        return `<li style="${itemStyle}" >${itemContent}</li>`
      }

      case 'heading': {
        const headingContent =
          node.children?.map((child) => convertNode(child)).join('') || ''
        const level = Math.min(Math.max(node.tag?.replace('h', '') || 1, 1), 6)
        return `<h${level} style="${style}">${headingContent}</h${level}>`
      }

      case 'linebreak': {
        return '<br>'
      }

      default: {
        if (node.children) {
          return node.children.map((child) => convertNode(child)).join('')
        }
        return ''
      }
    }
  }

  return editorState.root.children.map((child) => convertNode(child)).join('')
}
