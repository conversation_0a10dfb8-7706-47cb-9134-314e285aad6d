import { Resend } from 'resend'
import { WelcomeEmail } from '@components'
import { emailTypes } from '@constants'
import { render } from '@react-email/render'

const getEmailContent = async ({ type, data }) => {
  switch (type) {
    case emailTypes.WELCOME: {
      return {
        html: await render(
          WelcomeEmail({ firstName: data?.firstName, email: data.email })
        ),
        subject: 'Welcome to Swift Resume'
      }
    }
  }
}

export const sendEmail = async ({ type, to, data }) => {
  try {
    const resend = new Resend(process.env.RESEND_API_KEY)

    const emailContent = await getEmailContent({ type, data })

    const { data: emailData, error } = await resend.emails.send({
      from: `${process.env.RESEND_FROM_NAME} <${process.env.RESEND_FROM_EMAIL}>`,
      to: to,
      ...emailContent
    })

    if (error) {
      return console.error({ error })
    }

    return emailData
  } catch (error) {
    console.error('Error sending email:', error)
    throw error
  }
}
