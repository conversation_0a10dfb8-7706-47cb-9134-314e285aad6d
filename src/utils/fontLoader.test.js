// Simple test file to verify font loader functions
import { getFontConfig, getFontFamily, getFontUrl, isValidFontValue } from './fontLoader'

// Test data
console.log('Testing Font Loader Functions:')

// Test getFontConfig
console.log('1. getFontConfig("timeless"):', getFontConfig('timeless'))
console.log('2. getFontConfig("invalid"):', getFontConfig('invalid'))

// Test getFontFamily
console.log('3. getFontFamily("timeless"):', getFontFamily('timeless'))
console.log('4. getFontFamily("retro"):', getFontFamily('retro'))
console.log('5. getFontFamily("invalid"):', getFontFamily('invalid'))

// Test getFontUrl
console.log('6. getFontUrl("clean"):', getFontUrl('clean'))
console.log('7. getFontUrl("visionary"):', getFontUrl('visionary'))

// Test isValidFontValue
console.log('8. isValidFontValue("timeless"):', isValidFontValue('timeless'))
console.log('9. isValidFontValue("invalid"):', isValidFontValue('invalid'))

export {} // Make this a module
