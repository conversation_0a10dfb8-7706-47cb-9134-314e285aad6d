export const debounce = (function_, delay) => {
  let timeoutId
  let lastArguments

  const debouncedFunction = (...arguments_) => {
    lastArguments = arguments_
    clearTimeout(timeoutId)

    timeoutId = setTimeout(() => {
      function_(...arguments_)
    }, delay)
  }

  debouncedFunction.cancel = () => {
    clearTimeout(timeoutId)
    timeoutId = null
    lastArguments = null
  }

  debouncedFunction.flush = () => {
    if (timeoutId && lastArguments) {
      clearTimeout(timeoutId)
      function_(...lastArguments)
      timeoutId = null
      lastArguments = null
    }
  }

  debouncedFunction.pending = () => {
    return timeoutId != null
  }

  return debouncedFunction
}
