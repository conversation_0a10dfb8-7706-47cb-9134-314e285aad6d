import {
  BriefcaseBusiness,
  FilePlus,
  GraduationCap,
  Lightbulb,
  Smile,
  UserRound
} from 'lucide-react'
import { z } from 'zod'
import {
  ResumeAccentsBlur,
  ResumeAccentsHeader,
  ResumeAccentsNone,
  ResumeAccentsRibbon,
  ResumeFullSplit,
  ResumeFullWidth,
  ResumeHeaderDefault,
  ResumeHeaderDotted,
  ResumeHeaderSquiggle,
  ResumeHeaderUnderline,
  ResumeSidebarInsetStyle,
  ResumeSidebarLeft,
  ResumeSidebarNormalStyle,
  ResumeSidebarRight,
  ResumeSpacingLoose,
  ResumeSpacingNormal,
  ResumeSpacingTight
} from '@components'
import { collections, pageSizes } from '@constants'
import { getColorTheme, removeId } from '@utils'
import { validateDates } from './validateDates'

const { A4 } = pageSizes

export const months = [
  { value: '1', label: 'january' },
  { value: '2', label: 'february' },
  { value: '3', label: 'march' },
  { value: '4', label: 'april' },
  { value: '5', label: 'may' },
  { value: '6', label: 'june' },
  { value: '7', label: 'july' },
  { value: '8', label: 'august' },
  { value: '9', label: 'september' },
  { value: '10', label: 'october' },
  { value: '11', label: 'november' },
  { value: '12', label: 'december' }
]

export const resumeOptionValue = Object.freeze({
  sidebarLeft: 'sidebarLeft',
  sidebarRight: 'sidebarRight',
  fullWidth: 'fullWidth',
  fullSplit: 'fullSplit',
  normal: 'normal',
  inset: 'inset',
  tight: 'tight',
  loose: 'loose',
  ribbon: 'ribbon',
  blur: 'blur',
  header: 'header',
  none: 'none',
  timeless: 'timeless',
  retro: 'retro',
  clean: 'clean',
  visionary: 'visionary',
  default: 'default',
  underline: 'underline',
  dotted: 'dotted',
  squiggle: 'squiggle',
  cloud: 'cloud',
  zinc: 'zinc',
  electric: 'electric',
  pomegranate: 'pomegranate',
  emerald: 'emerald',
  royal: 'royal',
  ember: 'ember',
  blush: 'blush',
  slate: 'slate',
  stone: 'stone',
  square: 'square',
  rounded: 'rounded',
  circle: 'circle',
  personalDetails: 'personalDetails',
  experience: 'experience',
  skills: 'skills',
  education: 'education',
  proficiencies: 'proficiencies',
  customSections: 'customSections',
  design: 'design'
})

export const location = {
  main: 'main',
  sidebar: 'sidebar'
}

export const colors = {
  white: '#FFFFFF',
  zinc: { default: '#F4F4F5', 200: '#E4E4E7', 400: '#9F9FA9', 900: '#18181B' },
  electric: { default: '#2B7FFF', light: '#DBEAFE', skyBlue: '#E0E7FF' },
  pomegranate: { default: '#FF637E', light: '#FFE4E6' },
  emerald: { default: '#00BC7D', light: '#D0FAE5' },
  royal: { default: '#615FFF' },
  ember: { default: '#FD9A00', light: '#FEF3C6' },
  blush: { default: '#FCE7F3', dark: '#FB64B6' },
  slate: '#62748E',
  stone: '#79716B',
  black: { default: '#000000', light: '#18181B' },
  gray: {
    light: '#9F9FA9',
    dark: '#71717B',
    slate: '#CAD5E2',
    stone: '#D6D3D1',
    normal: '#F8FAFC',
    lightGray: '#90A1B9',
    stoneLight: '#FAFAF9'
  },
  pickerColor: {
    white:
      'conic-gradient(from 228deg at 50% 50%, #FFFFFF 0deg, #F8F9FA 72deg, #F1F3F5 144deg, #E9ECEF 216deg, #DEE2E6 288deg, #DDE1E3 360deg)',
    zinc: 'conic-gradient(from 228deg at 50% 50%, #F4F4F5 0deg, #E8E8E9 72deg, #DCDCDD 144deg, #D0D0D1 216deg, #C4C4C5 288deg, #B8B8B9 360deg)',
    electric:
      'conic-gradient(from 228deg at 50% 50%, #2B7FFF 0deg, #2274EF 72deg, #1A69E0 144deg, #125ED0 216deg, #0C54C1 288deg, #064BB1 360deg)',
    pomegranate:
      'conic-gradient(from 228deg at 50% 50%, #FF637E 0deg, #ED556F 72deg, #DB4760 144deg, #CA3B53 216deg, #B83046 288deg, #A6263B 360deg)',
    emerald:
      'conic-gradient(from 228deg at 50% 50%, #00BC7D 0deg, #01AA71 72deg, #019966 144deg, #02875A 216deg, #02764F 288deg, #026443 360deg)',
    royal:
      'conic-gradient(from 228deg at 50% 50%, #615FFF 0deg, #514FEB 72deg, #4240D7 144deg, #3533C2 216deg, #2927AE 288deg, #1F1D9A 360deg)',
    ember:
      'conic-gradient(from 228deg at 50% 50%, #FD9A00 0deg, #E18900 72deg, #C57800 144deg, #A86700 216deg, #8C5600 288deg, #704500 360deg)',
    blush:
      'conic-gradient(from 228deg at 50% 50%, #FCE7F3 0deg, #EACEDE 72deg, #D9B7CB 144deg, #C7A1B7 216deg, #B68CA4 288deg, #A47992 360deg)',
    slate:
      'conic-gradient(from 228deg at 50% 50%, #62748E 0deg, #55667F 72deg, #495970 144deg, #3E4C61 216deg, #323F52 288deg, #283343 360deg)',
    stone:
      'conic-gradient(from 228deg at 50% 50%, #79716B 0deg, #6C635C 72deg, #5F554E 144deg, #524841 216deg, #453B35 288deg, #382F29 360deg)'
  }
}

export const colorPickerGradient = {
  cloud:
    'conic-gradient(from 228deg at 50% 50%, #FFFFFF 0deg, #F8F9FA 72deg, #F1F3F5 144deg, #E9ECEF 216deg, #DEE2E6 288deg, #DDE1E3 360deg)',
  zinc: 'conic-gradient(from 228deg at 50% 50%, #F4F4F5 0deg, #E8E8E9 72deg, #DCDCDD 144deg, #D0D0D1 216deg, #C4C4C5 288deg, #B8B8B9 360deg)',
  electric:
    'conic-gradient(from 228deg at 50% 50%, #2B7FFF 0deg, #2274EF 72deg, #1A69E0 144deg, #125ED0 216deg, #0C54C1 288deg, #064BB1 360deg)',
  pomegranate:
    'conic-gradient(from 228deg at 50% 50%, #FF637E 0deg, #ED556F 72deg, #DB4760 144deg, #CA3B53 216deg, #B83046 288deg, #A6263B 360deg)',
  emerald:
    'conic-gradient(from 228deg at 50% 50%, #00BC7D 0deg, #01AA71 72deg, #019966 144deg, #02875A 216deg, #02764F 288deg, #026443 360deg)',
  royal:
    'conic-gradient(from 228deg at 50% 50%, #615FFF 0deg, #514FEB 72deg, #4240D7 144deg, #3533C2 216deg, #2927AE 288deg, #1F1D9A 360deg)',
  ember:
    'conic-gradient(from 228deg at 50% 50%, #FD9A00 0deg, #E18900 72deg, #C57800 144deg, #A86700 216deg, #8C5600 288deg, #704500 360deg)',
  blush:
    'conic-gradient(from 228deg at 50% 50%, #FCE7F3 0deg, #EACEDE 72deg, #D9B7CB 144deg, #C7A1B7 216deg, #B68CA4 288deg, #A47992 360deg)',
  slate:
    'conic-gradient(from 228deg at 50% 50%, #62748E 0deg, #55667F 72deg, #495970 144deg, #3E4C61 216deg, #323F52 288deg, #283343 360deg)',
  stone:
    'conic-gradient(from 228deg at 50% 50%, #79716B 0deg, #6C635C 72deg, #5F554E 144deg, #524841 216deg, #453B35 288deg, #382F29 360deg)'
}

export const theme = {
  zinc: {
    100: '#F4F4F5',
    200: '#E4E4E7',
    300: '#D4D4D8',
    400: '#9F9FA9',
    500: '#71717B',
    900: '#18181B'
  },
  blue: {
    100: '#DBEAFE',
    400: '#51A2FF',
    500: '#2B7FFF'
  },
  rose: {
    100: '#FFE4E6',
    300: '#FFA1AD',
    400: '#FF637E'
  },
  emerald: {
    100: '#D0FAE5',
    400: '#00D492',
    500: '#00BC7D'
  },
  indigo: {
    100: '#E0E7FF',
    200: '#C6D2FF',
    300: '#A3B3FF',
    500: '#615FFF'
  },
  amber: {
    100: '#FEF3C6',
    300: '#FFD230',
    500: '#FD9A00'
  },
  pink: {
    100: '#FCE7F3',
    200: '#FCCEE8',
    400: '#FB64B6'
  },
  slate: {
    50: '#F8FAFC',
    200: '#E2E8F0',
    300: '#CAD5E2',
    400: '#90A1B9',
    500: '#62748E'
  },
  stone: {
    50: '#FAFAF9',
    200: '#E7E5E4',
    300: '#D6D3D1',
    400: '#9F9FA9',
    500: '#79716B'
  },
  white: { default: '#FFFFFF', transparent: 'rgba(255, 255, 255, 0)' }
}

export const richTextTheme = {
  text: {
    bold: 'editor-text-bold'
  },
  list: {
    ul: 'rp-ul'
  }
}

export const editorSections = [
  {
    id: resumeOptionValue.personalDetails,
    icon: UserRound,
    title: 'personalDetails',
    description: 'yourNameSummaryImageAndTitle'
  },
  {
    id: resumeOptionValue.experience,
    icon: BriefcaseBusiness,
    title: 'experience',
    description: 'yourWorkHistoryAndAchievements'
  },
  {
    id: resumeOptionValue.skills,
    icon: Lightbulb,
    title: 'skills',
    description: 'keyAreasThatIllustrateYourStrengths'
  },
  {
    id: resumeOptionValue.education,
    icon: GraduationCap,
    title: 'education',
    description: 'whereYouStudiedAndYourQualifications'
  },
  {
    id: resumeOptionValue.proficiencies,
    icon: FilePlus,
    title: 'proficiencies',
    description: 'certificatesAndLanguages'
  },
  {
    id: resumeOptionValue.customSections,
    icon: Smile,
    title: 'customSections',
    description: 'showcaseUniqueExperiences'
  }
]

export const resumeOptions = Object.freeze({
  layout: {
    default: resumeOptionValue.sidebarLeft,
    options: [
      {
        label: 'Sidebar Left',
        value: resumeOptionValue.sidebarLeft
      },
      {
        label: 'Sidebar Right',
        value: resumeOptionValue.sidebarRight
      },
      {
        label: 'Full Width',
        value: resumeOptionValue.fullWidth
      },
      {
        label: 'Full Split',
        value: resumeOptionValue.fullSplit
      }
    ],
    icons: {
      [resumeOptionValue.sidebarLeft]: (props) => <ResumeSidebarLeft {...props} />,
      [resumeOptionValue.sidebarRight]: (props) => <ResumeSidebarRight {...props} />,
      [resumeOptionValue.fullWidth]: (props) => <ResumeFullWidth {...props} />,
      [resumeOptionValue.fullSplit]: (props) => <ResumeFullSplit {...props} />
    }
  },
  sidebarStyle: {
    default: resumeOptionValue.normal,
    options: [
      {
        label: 'Normal',
        value: resumeOptionValue.normal
      },
      {
        label: 'Inset',
        value: resumeOptionValue.inset
      }
    ],
    icons: {
      [resumeOptionValue.normal]: (props) => <ResumeSidebarNormalStyle {...props} />,
      [resumeOptionValue.inset]: (props) => <ResumeSidebarInsetStyle {...props} />
    }
  },
  spacing: {
    default: resumeOptionValue.normal,
    options: [
      {
        label: 'Tight',
        value: resumeOptionValue.tight
      },
      {
        label: 'Normal',
        value: resumeOptionValue.normal
      },
      {
        label: 'Loose',
        value: resumeOptionValue.loose
      }
    ],
    icons: {
      [resumeOptionValue.tight]: (props) => <ResumeSpacingTight {...props} />,
      [resumeOptionValue.normal]: (props) => <ResumeSpacingNormal {...props} />,
      [resumeOptionValue.loose]: (props) => <ResumeSpacingLoose {...props} />
    },
    styles: {
      [resumeOptionValue.tight]: {
        gap: '16px'
      },
      [resumeOptionValue.normal]: {
        gap: '24px'
      },
      [resumeOptionValue.loose]: {
        gap: '32px'
      }
    }
  },
  accent: {
    default: resumeOptionValue.none,
    options: [
      {
        label: 'None',
        value: resumeOptionValue.none
      },
      {
        label: 'Ribbon',
        value: resumeOptionValue.ribbon
      },
      {
        label: 'Blur',
        value: resumeOptionValue.blur
      },
      {
        label: 'Header',
        value: resumeOptionValue.header
      }
    ],
    icons: {
      [resumeOptionValue.ribbon]: (props) => <ResumeAccentsRibbon {...props} />,
      [resumeOptionValue.blur]: (props) => <ResumeAccentsBlur {...props} />,
      [resumeOptionValue.header]: (props) => <ResumeAccentsHeader {...props} />,
      [resumeOptionValue.none]: (props) => <ResumeAccentsNone {...props} />
    },
    styles: {
      [resumeOptionValue.ribbon]: {
        bandHeight: '8px'
      },
      [resumeOptionValue.blur]: {
        backgroundImage: 'smudge.svg'
      },
      [resumeOptionValue.header]: {
        backgroundImage: 'headerbg.svg'
      }
    }
  },
  accentOptions: {
    default: null,
    options: []
  },
  font: {
    default: resumeOptionValue.timeless,
    options: [
      {
        label: 'Timeless',
        value: resumeOptionValue.timeless,
        fontFamily: '"Inter", sans-serif',
        url: 'https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap'
      },
      {
        label: 'Retro',
        value: resumeOptionValue.retro,
        fontFamily: '"Montserrat", sans-serif',
        url: 'https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap'
      },
      {
        label: 'Clean',
        value: resumeOptionValue.clean,
        fontFamily: '"Poppins", sans-serif',
        url: 'https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap'
      },
      {
        label: 'Visionary',
        value: resumeOptionValue.visionary,
        fontFamily: '"Raleway", sans-serif',
        url: 'https://fonts.googleapis.com/css2?family=Raleway:ital,wght@0,100..900;1,100..900&display=swap'
      }
    ]
  },
  headingStyle: {
    default: resumeOptionValue.default,
    options: [
      {
        label: 'Default',
        value: resumeOptionValue.default
      }
    ]
  },
  headingUnderlineStyle: {
    default: resumeOptionValue.default,
    options: [
      {
        label: 'Default',
        value: resumeOptionValue.default
      },
      {
        label: 'Underline',
        value: resumeOptionValue.underline
      },
      {
        label: 'Dotted',
        value: resumeOptionValue.dotted
      },
      {
        label: 'Squiggle',
        value: resumeOptionValue.squiggle
      }
    ],
    icons: {
      [resumeOptionValue.default]: (props) => <ResumeHeaderDefault {...props} />,
      [resumeOptionValue.underline]: (props) => <ResumeHeaderUnderline {...props} />,
      [resumeOptionValue.dotted]: (props) => <ResumeHeaderDotted {...props} />,
      [resumeOptionValue.squiggle]: (props) => <ResumeHeaderSquiggle {...props} />
    },
    styles: {
      [resumeOptionValue.default]: {
        borderWidth: '0px',
        borderStyle: 'solid',
        padding: '0px 0px 6px 0px'
      },
      [resumeOptionValue.underline]: {
        borderWidth: '1px',
        borderStyle: 'solid',
        padding: '0px 0px 6px 0px'
      },
      [resumeOptionValue.dotted]: {
        borderWidth: '1px',
        borderStyle: 'dotted',
        padding: '0px 0px 6px 0px'
      },
      [resumeOptionValue.squiggle]: {
        borderWidth: '1px',
        borderStyle: 'dashed',
        padding: '0px 0px 6px 0px'
      }
    }
  },
  palette: {
    default: resumeOptionValue.cloud,
    options: [
      {
        label: 'Cloud',
        value: resumeOptionValue.cloud
      },
      {
        label: 'Zinc',
        value: resumeOptionValue.zinc
      },
      {
        label: 'Electric',
        value: resumeOptionValue.electric
      },
      {
        label: 'Pomegranate',
        value: resumeOptionValue.pomegranate
      },
      {
        label: 'Emerald',
        value: resumeOptionValue.emerald
      },
      {
        label: 'Royal',
        value: resumeOptionValue.royal
      },
      {
        label: 'Ember',
        value: resumeOptionValue.ember
      },
      {
        label: 'Blush',
        value: resumeOptionValue.blush
      },
      {
        label: 'Slate',
        value: resumeOptionValue.slate
      },
      {
        label: 'Stone',
        value: resumeOptionValue.stone
      }
    ],
    styles: {
      [resumeOptionValue.cloud]: {
        sidebar: {
          name: theme.zinc[900],
          contactLabel: theme.zinc[400],
          contact: theme.zinc[900],
          sectionTitle: theme.zinc[400],
          sectionTitleBorder: theme.zinc[200],
          mainText: theme.zinc[900],
          fill: theme.white.transparent
        },
        main: {
          name: theme.zinc[900],
          contactLabel: theme.zinc[400],
          contact: theme.zinc[900],
          sectionTitle: theme.zinc[400],
          sectionTitleBorder: theme.zinc[200],
          mainText: theme.zinc[900],
          fill: theme.white.default
        },
        dark: {
          name: theme.white.default,
          contactLabel: theme.white.default,
          contact: theme.white.default,
          sectionTitle: theme.white.default,
          sectionTitleBorder: theme.white.default,
          mainText: theme.white.default,
          fill: theme.white.transparent
        }
      },
      [resumeOptionValue.zinc]: {
        sidebar: {
          name: theme.zinc[900],
          contactLabel: theme.zinc[500],
          contact: theme.zinc[900],
          sectionTitle: theme.zinc[500],
          sectionTitleBorder: theme.zinc[300],
          mainText: theme.zinc[900],
          fill: theme.zinc[100]
        },
        main: {
          name: theme.zinc[900],
          contactLabel: theme.zinc[500],
          contact: theme.zinc[900],
          sectionTitle: theme.zinc[500],
          sectionTitleBorder: theme.zinc[300],
          mainText: theme.zinc[900],
          fill: theme.white.default
        },
        dark: {
          name: theme.white.default,
          contactLabel: theme.white.default,
          contact: theme.white.default,
          sectionTitle: theme.white.default,
          sectionTitleBorder: theme.white.default,
          mainText: theme.white.default,
          fill: theme.white.transparent
        }
      },
      [resumeOptionValue.electric]: {
        sidebar: {
          name: theme.zinc[900],
          contactLabel: theme.blue[100],
          contact: theme.white.default,
          sectionTitle: theme.blue[100],
          sectionTitleBorder: theme.blue[400],
          mainText: theme.white.default,
          fill: theme.blue[500]
        },
        main: {
          name: theme.zinc[900],
          contactLabel: theme.zinc[400],
          contact: theme.zinc[900],
          sectionTitle: theme.blue[500],
          sectionTitleBorder: theme.blue[100],
          mainText: theme.zinc[900],
          fill: theme.white.default
        },
        dark: {
          name: theme.white.default,
          contactLabel: theme.white.default,
          contact: theme.white.default,
          sectionTitle: theme.white.default,
          sectionTitleBorder: theme.white.default,
          mainText: theme.white.default,
          fill: theme.white.transparent
        }
      },
      [resumeOptionValue.pomegranate]: {
        sidebar: {
          name: theme.zinc[900],
          contactLabel: theme.rose[100],
          contact: theme.white.default,
          sectionTitle: theme.rose[100],
          sectionTitleBorder: theme.rose[300],
          mainText: theme.white.default,
          fill: theme.rose[400]
        },
        main: {
          name: theme.zinc[900],
          contactLabel: theme.zinc[400],
          contact: theme.zinc[900],
          sectionTitle: theme.rose[400],
          sectionTitleBorder: theme.rose[100],
          mainText: theme.zinc[900],
          fill: theme.white.default
        },
        dark: {
          name: theme.white.default,
          contactLabel: theme.white.default,
          contact: theme.white.default,
          sectionTitle: theme.white.default,
          sectionTitleBorder: theme.white.default,
          mainText: theme.white.default,
          fill: theme.white.transparent
        }
      },
      [resumeOptionValue.emerald]: {
        sidebar: {
          name: theme.white.default,
          contactLabel: theme.emerald[100],
          contact: theme.white.default,
          sectionTitle: theme.emerald[100],
          sectionTitleBorder: theme.emerald[400],
          mainText: theme.white.default,
          fill: theme.emerald[500]
        },
        main: {
          name: theme.zinc[900],
          contactLabel: theme.zinc[400],
          contact: theme.zinc[900],
          sectionTitle: theme.emerald[500],
          sectionTitleBorder: theme.zinc[200],
          mainText: theme.zinc[900],
          fill: theme.white.default
        },
        dark: {
          name: theme.white.default,
          contactLabel: theme.white.default,
          contact: theme.white.default,
          sectionTitle: theme.white.default,
          sectionTitleBorder: theme.white.default,
          mainText: theme.white.default,
          fill: theme.white.transparent
        }
      },
      [resumeOptionValue.royal]: {
        sidebar: {
          name: theme.white.default,
          contactLabel: theme.indigo[100],
          contact: theme.white.default,
          sectionTitle: theme.indigo[100],
          sectionTitleBorder: theme.indigo[300],
          mainText: theme.white.default,
          fill: theme.indigo[500]
        },
        main: {
          name: theme.zinc[900],
          contactLabel: theme.zinc[400],
          contact: theme.zinc[900],
          sectionTitle: theme.indigo[500],
          sectionTitleBorder: theme.indigo[200],
          mainText: theme.zinc[900],
          fill: theme.white.default
        },
        dark: {
          name: theme.white.default,
          contactLabel: theme.white.default,
          contact: theme.white.default,
          sectionTitle: theme.white.default,
          sectionTitleBorder: theme.white.default,
          mainText: theme.white.default,
          fill: theme.white.transparent
        }
      },
      [resumeOptionValue.ember]: {
        sidebar: {
          name: theme.white.default,
          contactLabel: theme.amber[100],
          contact: theme.white.default,
          sectionTitle: theme.amber[100],
          sectionTitleBorder: theme.amber[300],
          mainText: theme.white.default,
          fill: theme.amber[500]
        },
        main: {
          name: theme.zinc[900],
          contactLabel: theme.zinc[400],
          contact: theme.zinc[900],
          sectionTitle: theme.amber[500],
          sectionTitleBorder: theme.zinc[200],
          mainText: theme.zinc[900],
          fill: theme.white.default
        },
        dark: {
          name: theme.white.default,
          contactLabel: theme.white.default,
          contact: theme.white.default,
          sectionTitle: theme.white.default,
          sectionTitleBorder: theme.white.default,
          mainText: theme.white.default,
          fill: theme.white.transparent
        }
      },
      [resumeOptionValue.blush]: {
        sidebar: {
          name: theme.white.default,
          contactLabel: theme.zinc[500],
          contact: theme.zinc[900],
          sectionTitle: theme.zinc[500],
          sectionTitleBorder: theme.pink[200],
          mainText: theme.zinc[900],
          fill: theme.pink[100]
        },
        main: {
          name: theme.zinc[900],
          contactLabel: theme.zinc[400],
          contact: theme.zinc[900],
          sectionTitle: theme.pink[400],
          sectionTitleBorder: theme.pink[200],
          mainText: theme.zinc[900],
          fill: theme.white.default
        },
        dark: {
          name: theme.white.default,
          contactLabel: theme.white.default,
          contact: theme.white.default,
          sectionTitle: theme.white.default,
          sectionTitleBorder: theme.white.default,
          mainText: theme.white.default,
          fill: theme.white.transparent
        }
      },
      [resumeOptionValue.slate]: {
        sidebar: {
          name: theme.zinc[900],
          contactLabel: theme.slate[300],
          contact: theme.white.default,
          sectionTitle: theme.slate[300],
          sectionTitleBorder: theme.slate[400],
          mainText: theme.white.default,
          fill: theme.slate[500]
        },
        main: {
          name: theme.zinc[900],
          contactLabel: theme.slate[400],
          contact: theme.zinc[900],
          sectionTitle: theme.slate[500],
          sectionTitleBorder: theme.slate[200],
          mainText: theme.zinc[900],
          fill: theme.slate[50]
        },
        dark: {
          name: theme.white.default,
          contactLabel: theme.white.default,
          contact: theme.white.default,
          sectionTitle: theme.white.default,
          sectionTitleBorder: theme.white.default,
          mainText: theme.white.default,
          fill: theme.white.transparent
        }
      },
      [resumeOptionValue.stone]: {
        sidebar: {
          name: theme.white.default,
          contactLabel: theme.stone[300],
          contact: theme.white.default,
          sectionTitle: theme.stone[300],
          sectionTitleBorder: theme.stone[400],
          mainText: theme.white.default,
          fill: theme.stone[500]
        },
        main: {
          name: theme.zinc[900],
          contactLabel: theme.zinc[400],
          contact: theme.zinc[900],
          sectionTitle: theme.stone[400],
          sectionTitleBorder: theme.stone[200],
          mainText: theme.zinc[900],
          fill: theme.stone[50]
        },
        dark: {
          name: theme.white.default,
          contactLabel: theme.white.default,
          contact: theme.white.default,
          sectionTitle: theme.white.default,
          sectionTitleBorder: theme.white.default,
          mainText: theme.white.default,
          fill: theme.white.transparent
        }
      }
    }
  },
  avatarStyle: {
    default: resumeOptionValue.square,
    options: [
      {
        label: 'Square',
        value: resumeOptionValue.square
      },
      {
        label: 'Rounded',
        value: resumeOptionValue.rounded
      },
      {
        label: 'Circle',
        value: resumeOptionValue.circle
      }
    ],
    styles: {
      [resumeOptionValue.square]: {
        borderRadius: '0px'
      },
      [resumeOptionValue.rounded]: {
        borderRadius: '8px'
      },
      [resumeOptionValue.circle]: {
        borderRadius: '50%'
      }
    }
  },
  backgroundPureWhite: {
    default: false
  },
  pageNumbers: {
    default: false
  }
})

export const templateFields = [
  {
    type: 'row',
    fields: [
      {
        name: 'layout',
        type: 'select',
        options: resumeOptions.layout.options,
        defaultValue: resumeOptions.layout.default,
        admin: {
          width: '33%'
        }
      },
      {
        name: 'sidebarStyle',
        type: 'select',
        options: resumeOptions.sidebarStyle.options,
        defaultValue: resumeOptions.sidebarStyle.default,
        admin: {
          width: '33%'
        }
      },
      {
        name: 'spacing',
        type: 'select',
        options: resumeOptions.spacing.options,
        defaultValue: resumeOptions.spacing.default,
        admin: {
          width: '33%'
        }
      }
    ]
  },
  {
    type: 'row',
    fields: [
      {
        name: 'accent',
        type: 'select',
        options: resumeOptions.accent.options,
        defaultValue: resumeOptions.accent.default,
        admin: {
          width: '33%'
        }
      },
      {
        name: 'font',
        type: 'select',
        options: resumeOptions.font.options,
        defaultValue: resumeOptions.font.default,
        admin: {
          width: '33%'
        }
      },
      {
        name: 'headingStyle',
        type: 'select',
        options: resumeOptions.headingStyle.options,
        defaultValue: resumeOptions.headingStyle.default,
        admin: {
          width: '33%'
        }
      }
    ]
  },
  {
    type: 'row',
    fields: [
      {
        name: 'headingUnderlineStyle',
        type: 'select',
        options: resumeOptions.headingUnderlineStyle.options,
        defaultValue: resumeOptions.headingUnderlineStyle.default,
        admin: {
          width: '33%'
        }
      },
      {
        name: 'palette',
        type: 'select',
        options: resumeOptions.palette.options,
        defaultValue: resumeOptions.palette.default,
        admin: {
          width: '33%'
        }
      },
      {
        name: 'avatarStyle',
        type: 'select',
        options: resumeOptions.avatarStyle.options,
        defaultValue: resumeOptions.avatarStyle.default,
        admin: {
          width: '33%'
        }
      }
    ]
  },
  {
    type: 'row',
    fields: [
      {
        name: 'accentOptions',
        type: 'select',
        options: resumeOptions.accentOptions.options,
        defaultValue: resumeOptions.accentOptions.default,
        required: false,
        admin: {
          width: '33%'
        }
      },
      {
        name: 'backgroundPureWhite',
        type: 'checkbox',
        defaultValue: resumeOptions.backgroundPureWhite.default,
        admin: {
          width: '33%',
          style: {
            marginTop: '25px'
          }
        }
      },
      {
        name: 'pageNumbers',
        type: 'checkbox',
        defaultValue: resumeOptions.pageNumbers.default,
        admin: {
          width: '33%',
          style: {
            marginTop: '25px'
          }
        }
      }
    ]
  },
  {
    name: 'sections',
    type: 'array',
    fields: [
      {
        type: 'row',
        fields: [
          {
            name: 'sectionKey',
            label: 'Section',
            type: 'select',
            required: true,
            options: [
              {
                label: 'Personal Details',
                value: resumeOptionValue.personalDetails
              },
              {
                label: 'Experience',
                value: resumeOptionValue.experience
              },
              {
                label: 'Skills',
                value: resumeOptionValue.skills
              },
              {
                label: 'Education',
                value: resumeOptionValue.education
              },
              {
                label: 'Additional proficiencies',
                value: resumeOptionValue.proficiencies
              },
              {
                label: 'Custom sections',
                value: resumeOptionValue.customSections
              }
            ],
            admin: {
              width: '33.33%',
              isClearable: false
            }
          },
          {
            name: 'showInSidebar', // required
            type: 'checkbox', // required
            label: 'Show in sidebar when sidebar is enabled',
            defaultValue: false,
            admin: {
              width: '33.33%',
              style: {
                marginTop: '25px'
              }
            }
          },
          {
            name: 'isVisible',
            type: 'checkbox',
            label: 'Visible',
            defaultValue: true,
            admin: {
              width: '33.33%',
              style: {
                marginTop: '25px'
              }
            }
          },
          {
            name: 'sectionName',
            type: 'text',
            label: 'Section name',
            defaultValue: true,
            admin: {
              width: '33.33%',
              style: {
                marginTop: '25px'
              }
            }
          }
        ]
      }
    ],
    defaultValue: [
      {
        sectionKey: resumeOptionValue.personalDetails,
        showInSidebar: false,
        isVisible: true,
        sectionName: resumeOptionValue.personalDetails
      },
      {
        sectionKey: resumeOptionValue.skills,
        showInSidebar: true,
        isVisible: true,
        sectionName: resumeOptionValue.skills
      },
      {
        sectionKey: resumeOptionValue.experience,
        showInSidebar: false,
        isVisible: true,
        sectionName: resumeOptionValue.experience
      },
      {
        sectionKey: resumeOptionValue.education,
        showInSidebar: false,
        isVisible: true,
        sectionName: resumeOptionValue.education
      },
      {
        sectionKey: resumeOptionValue.proficiencies,
        showInSidebar: true,
        isVisible: true,
        sectionName: resumeOptionValue.proficiencies
      },
      {
        sectionKey: resumeOptionValue.customSections,
        showInSidebar: false,
        isVisible: true,
        sectionName: resumeOptionValue.customSections
      }
    ]
  }
]

export const resumeFields = [
  {
    name: 'name',
    type: 'text',
    minLength: collections.shared.minLength,
    maxLength: collections.shared.maxLength
  },
  {
    name: 'user',
    type: 'relationship',
    relationTo: collections.users.slug,
    hasMany: false
  },
  {
    type: 'tabs',
    tabs: [
      {
        label: 'Personal Details',
        description: 'Your name, summary, image and title',
        fields: [
          {
            type: 'row',
            fields: [
              {
                name: 'profileImage',
                type: 'upload',
                relationTo: 'media',
                required: false,
                admin: {
                  description: 'Add a profile image',
                  width: '30%'
                }
              },
              {
                name: 'showProfileImage',
                type: 'checkbox',
                label: 'Show',
                defaultValue: true,
                admin: {
                  width: '70%',
                  style: {
                    marginTop: '25px'
                  }
                }
              }
            ]
          },
          {
            name: 'jobTitle',
            type: 'text',
            label: 'Job Title'
          },
          {
            name: 'summary',
            type: 'richText',
            label: 'Summary'
          },
          {
            type: 'row',
            fields: [
              {
                name: 'firstName',
                type: 'text',
                admin: {
                  width: '50%'
                }
              },
              {
                name: 'lastName',
                type: 'text',
                admin: {
                  width: '50%'
                }
              }
            ]
          },
          {
            type: 'row',
            fields: [
              {
                name: 'phone',
                type: 'text',
                admin: {
                  width: '50%'
                }
              },
              {
                name: 'email',
                type: 'text',
                admin: {
                  width: '50%'
                }
              }
            ]
          },
          {
            name: 'links',
            type: 'array',
            fields: [
              {
                type: 'row',
                fields: [
                  {
                    name: 'name',
                    type: 'text',
                    required: false,
                    admin: {
                      width: '50%'
                    }
                  },
                  {
                    name: 'url',
                    type: 'text',
                    required: false,
                    admin: {
                      width: '50%'
                    }
                  }
                ]
              }
            ],
            defaultValue: [
              {
                name: '',
                url: ''
              }
            ]
          }
        ]
      },
      {
        label: 'Experience',
        description: 'Your work history and achievements',
        fields: [
          {
            name: 'experience',
            type: 'array',
            fields: [
              {
                type: 'row',
                fields: [
                  {
                    name: 'title',
                    type: 'text',
                    required: false,
                    admin: {
                      width: '50%'
                    }
                  },
                  {
                    name: 'subTitle',
                    type: 'text',
                    admin: {
                      width: '50%'
                    }
                  }
                ]
              },
              {
                name: 'isInternship',
                type: 'checkbox',
                label: 'Mark as internship'
              },
              {
                type: 'row',
                fields: [
                  {
                    name: 'startMonth',
                    type: 'text',
                    required: false,
                    admin: {
                      width: '25%'
                    }
                  },
                  {
                    name: 'startYear',
                    type: 'text',
                    required: false,
                    admin: {
                      width: '25%'
                    }
                  },
                  {
                    name: 'endMonth',
                    type: 'text',
                    required: false,
                    admin: {
                      width: '20%'
                    }
                  },
                  {
                    name: 'endYear',
                    type: 'text',
                    admin: {
                      width: '20%'
                    }
                  },
                  {
                    name: 'isPresent',
                    type: 'checkbox',
                    label: 'Present',
                    admin: {
                      width: '10%',
                      style: {
                        marginTop: '25px'
                      }
                    }
                  }
                ]
              },
              {
                name: 'description',
                type: 'richText',
                label: 'Key accomplishments'
              },
              {
                name: 'isVisible',
                type: 'checkbox',
                label: 'Visible',
                defaultValue: true
              }
            ]
          }
        ]
      },
      {
        label: 'Skills',
        description: 'Key areas that illustrate your strengths',
        fields: [
          {
            name: 'skills',
            type: 'array',
            fields: [
              {
                type: 'row',
                fields: [
                  {
                    name: 'skill',
                    type: 'text',
                    required: false,
                    admin: {
                      width: '60%'
                    }
                  },
                  {
                    name: 'rating',
                    type: 'number',
                    min: 0,
                    max: 5,
                    step: 0.5,
                    defaultValue: 0,
                    admin: {
                      width: '30%',
                      step: 0.5
                    }
                  },
                  {
                    name: 'isVisible',
                    type: 'checkbox',
                    label: 'Visible',
                    defaultValue: true,
                    admin: {
                      width: '10%',
                      style: {
                        marginTop: '25px'
                      }
                    }
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        label: 'Education',
        description: 'Where you studied and your qualifications',
        fields: [
          {
            name: 'education',
            type: 'array',
            fields: [
              {
                type: 'row',
                fields: [
                  {
                    name: 'title',
                    type: 'text',
                    required: false,
                    admin: {
                      width: '50%'
                    }
                  },
                  {
                    name: 'subTitle',
                    type: 'text',
                    admin: {
                      width: '50%'
                    }
                  }
                ]
              },
              {
                type: 'row',
                fields: [
                  {
                    name: 'startMonth',
                    type: 'text',
                    required: false,
                    admin: {
                      width: '25%'
                    }
                  },
                  {
                    name: 'startYear',
                    type: 'text',
                    required: false,
                    admin: {
                      width: '25%'
                    }
                  },
                  {
                    name: 'endMonth',
                    type: 'text',
                    required: false,
                    admin: {
                      width: '20%'
                    }
                  },
                  {
                    name: 'endYear',
                    type: 'text',
                    admin: {
                      width: '20%'
                    }
                  },
                  {
                    name: 'isPresent',
                    type: 'checkbox',
                    label: 'Present',
                    admin: {
                      width: '10%',
                      style: {
                        marginTop: '25px'
                      }
                    }
                  }
                ]
              },
              {
                name: 'description',
                type: 'richText',
                label: 'Description'
              },
              {
                name: 'isVisible',
                type: 'checkbox',
                label: 'Visible',
                defaultValue: true
              }
            ]
          }
        ]
      },
      {
        label: 'Additional proficiencies',
        description: 'Certificates and languages',
        fields: [
          {
            name: 'proficiencies',
            type: 'array',
            fields: [
              {
                type: 'row',
                fields: [
                  {
                    name: 'title',
                    type: 'text',
                    required: false,
                    admin: {
                      width: '50%'
                    }
                  },
                  {
                    name: 'subTitle',
                    type: 'text',
                    admin: {
                      width: '50%'
                    }
                  }
                ]
              },
              {
                type: 'row',
                fields: [
                  {
                    name: 'startMonth',
                    type: 'text',
                    admin: {
                      width: '40%'
                    }
                  },
                  {
                    name: 'startYear',
                    type: 'text',
                    admin: {
                      width: '40%'
                    }
                  },
                  {
                    name: 'showDate',
                    type: 'checkbox',
                    label: 'Show date',
                    defaultValue: false,
                    admin: {
                      width: '20%'
                    }
                  }
                ]
              },
              {
                name: 'description',
                type: 'richText',
                label: 'Description',
                admin: {
                  placeholder: 'Describe your certification'
                }
              },
              {
                name: 'isVisible',
                type: 'checkbox',
                label: 'Visible',
                defaultValue: true
              }
            ]
          }
        ]
      },
      {
        label: 'Custom sections',
        description: 'Showcase unique experiences',
        fields: [
          {
            name: 'customSections',
            type: 'array',
            fields: [
              {
                type: 'row',
                fields: [
                  {
                    name: 'title',
                    type: 'text',
                    required: false,
                    admin: {
                      width: '50%',
                      placeholder: 'AWS Certified Cloud Practitioner'
                    }
                  },
                  {
                    name: 'subTitle',
                    type: 'text',
                    admin: {
                      width: '50%'
                    }
                  }
                ]
              },
              {
                type: 'row',
                fields: [
                  {
                    name: 'startMonth',
                    type: 'text',
                    admin: {
                      width: '40%',
                      placeholder: 'December'
                    }
                  },
                  {
                    name: 'startYear',
                    type: 'text',
                    admin: {
                      width: '40%',
                      placeholder: '2020'
                    }
                  },
                  {
                    name: 'showDate',
                    type: 'checkbox',
                    label: 'Show date',
                    defaultValue: false,
                    admin: {
                      width: '20%'
                    }
                  }
                ]
              },
              {
                name: 'description',
                type: 'richText',
                label: 'Description',
                admin: {
                  placeholder: 'Describe your certification'
                }
              },
              {
                name: 'isVisible',
                type: 'checkbox',
                label: 'Visible',
                defaultValue: true
              }
            ]
          }
        ]
      },
      {
        label: 'Design',
        description: 'Customize your resume appearance',
        fields: [
          {
            type: 'row',
            fields: [
              {
                name: 'templateId',
                type: 'relationship',
                relationTo: collections.templates.slug,
                hasMany: false,
                admin: {
                  width: '50%'
                }
              },
              {
                name: 'templateName',
                type: 'text',
                admin: {
                  width: '50%'
                }
              }
            ]
          },
          {
            name: 'design',
            type: 'group',
            fields: templateFields
          }
        ]
      }
    ]
  }
]

export const resumeStyles = {
  page: (palette, backgroundPureWhite) => {
    const resumeTheme = getColorTheme(palette)
    return {
      width: A4.width,
      height: A4.height,
      backgroundColor: backgroundPureWhite ? theme.white.default : resumeTheme.fill,
      position: 'relative',
      overflow: 'hidden'
    }
  },
  layout: (layout, hasSidebar) => {
    const rowStyle = layout === resumeOptionValue.sidebarLeft ? 'row-reverse' : 'row'
    return {
      display: 'flex',
      width: '100%',
      height: '100%',
      position: 'relative',
      flexDirection: hasSidebar ? rowStyle : 'column'
    }
  },
  mainOuter: () => ({
    flex: '1 1 0%'
  }),
  sidebarOuter: () => ({
    flexShrink: '0'
  }),
  mainContainer: (hasSidebar) => {
    return {
      width: hasSidebar ? A4.width * 0.67 : A4.width,
      height: '100%',
      padding: '30px'
    }
  },
  sidebarLayout: (sidebarStyle) => {
    const padding = sidebarStyle === resumeOptionValue.inset ? 10 : 0
    return {
      width: A4.width * 0.33,
      height: '100%',
      padding: padding + 'px'
    }
  },
  sidebarInnerContainer: (palette, sidebarStyle) => {
    const theme = getColorTheme(palette, 'sidebar')
    const borderRadius = sidebarStyle === resumeOptionValue.inset ? 10 : 0
    return {
      padding: '30px',
      backgroundColor: theme.fill,
      color: theme.mainText,
      borderRadius: borderRadius + 'px',
      height: '100%'
    }
  },
  sectionsContainer: (spacing) => {
    const spacingStyles = resumeOptions.spacing.styles[spacing]
    const gap = spacingStyles?.gap || '26px'
    return {
      display: 'flex',
      flexDirection: 'column',
      gap: gap
    }
  },
  sectionTitle: (layout) => {
    const width = layout === resumeOptionValue.fullSplit ? '85px' : '100%'
    return {
      width: '100%',
      maxWidth: width,
      flexShrink: 0
    }
  },
  sectionTitleSpan: (layout, palette, location, headingUnderlineStyle) => {
    const theme = getColorTheme(palette, location)
    const titleColor = theme?.sectionTitle
    const sectionTitleBorderColor = theme?.sectionTitleBorder
    const underlineStyles =
      resumeOptions.headingUnderlineStyle.styles[headingUnderlineStyle]
    const width = layout === resumeOptionValue.fullSplit ? 'max-content' : '100%'
    return {
      fontSize: '10px',
      lineHeight: '12px',
      fontWeight: '600',
      color: titleColor,
      letterSpacing: '-0.1px',
      textTransform: 'capitalize',
      display: 'flex',
      width: width,
      flexShrink: 0,
      ...(underlineStyles !== 'default' && {
        borderBottomWidth: underlineStyles?.borderWidth,
        borderBottomStyle: underlineStyles?.borderStyle,
        borderBottomColor: sectionTitleBorderColor,
        padding: underlineStyles?.padding
      })
    }
  },
  contactContainer: (hasSidebar) => ({
    display: 'flex',
    flexDirection: 'column',
    gap: '6px',
    width: hasSidebar ? '100%' : '60%',
    alignItems: hasSidebar ? 'flex-start' : 'flex-end',
    position: 'relative'
  }),
  jobTitle: (palette, location, accent, hasSidebar) => {
    const resumeTheme = getColorTheme(palette, location)
    const isWhite = accent === resumeOptionValue.header && !hasSidebar
    const jobTitleColor = isWhite ? theme.white.default : resumeTheme?.name
    return {
      fontSize: '14px',
      lineHeight: '17px',
      fontWeight: '400',
      color: jobTitleColor,
      letterSpacing: '-0.14px'
    }
  },
  contactItem: (hasSidebar) => ({
    display: 'flex',
    flexDirection: hasSidebar ? 'column' : 'row',
    justifyContent: hasSidebar ? 'flex-start' : 'flex-end',
    alignItems: hasSidebar ? 'flex-start' : 'center',
    gap: '4px',
    textAlign: hasSidebar ? 'left' : 'right',
    ...(!hasSidebar && {
      width: '100%'
    })
  }),
  personalInfoTitle: (palette, location, accent, hasSidebar) => {
    const resumeTheme = getColorTheme(palette, location)
    const titleColor = hasSidebar
      ? resumeTheme?.contactLabel
      : accent === resumeOptionValue.header
        ? theme.white.default
        : resumeTheme?.contactLabel

    return {
      fontSize: '10px',
      lineHeight: '12px',
      fontWeight: '400',
      color: titleColor,
      letterSpacing: '-0.1px',
      textTransform: 'capitalize'
    }
  },
  personalInfoDetail: (hasSidebar, palette, location, accent) => {
    const resumeTheme = getColorTheme(palette, location)
    const detailColor = hasSidebar
      ? resumeTheme?.contact
      : accent === resumeOptionValue.header
        ? theme.white.default
        : resumeTheme?.contact

    let truncateStyle = {}

    if (!hasSidebar) {
      truncateStyle = {
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap'
      }
    }

    return {
      fontSize: '10px',
      lineHeight: '12px',
      fontWeight: '400',
      color: detailColor,
      letterSpacing: '-0.1px',
      wordBreak: 'break-all',
      ...truncateStyle
    }
  },
  monthYear: (palette, location) => {
    const theme = getColorTheme(palette, location)
    return {
      fontSize: '10px',
      lineHeight: '12px',
      fontWeight: '400',
      color: theme?.mainText,
      letterSpacing: '-0.1px'
    }
  },
  userProfileContainer: (hasSidebar) => {
    return {
      display: 'flex',
      flexDirection: 'column',
      width: hasSidebar ? '100%' : '40%',
      position: 'relative'
    }
  },
  profileHeader: (accent, hasSidebar) => {
    const isHeaderAccent = accent === resumeOptionValue.header && !hasSidebar
    return {
      position: 'relative',
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: '12px',
      padding: isHeaderAccent ? '16px' : '0px',
      borderRadius: isHeaderAccent ? '10px' : '0px',
      overflow: 'hidden'
    }
  },
  userName: (palette, location, accent, hasSidebar) => {
    const resumeTheme = getColorTheme(palette, location)
    const isWhite = accent === resumeOptionValue.header && !hasSidebar
    const nameColor = isWhite ? theme.white.default : resumeTheme?.name
    return {
      fontSize: '14px',
      lineHeight: '17px',
      fontWeight: '600',
      color: nameColor,
      letterSpacing: '-0.14px'
    }
  },
  userPictureContainer: () => ({
    overflow: 'hidden',
    marginBottom: '8px'
  }),
  userPicture: (avatarStyle) => {
    const borderRadius = resumeOptions.avatarStyle.styles[avatarStyle].borderRadius
    return {
      width: '70px',
      height: '70px',
      objectFit: 'cover',
      borderRadius: borderRadius
    }
  },
  richText: (palette, location) => {
    const theme = getColorTheme(palette, location)
    return {
      fontSize: '11px',
      lineHeight: '14px',
      fontWeight: '400',
      color: theme?.mainText,
      letterSpacing: '-0.11px'
    }
  },
  skillsList: () => ({
    display: 'flex',
    flexDirection: 'column',
    gap: '8px'
  }),
  skillListRow: () => ({
    display: 'flex',
    flexDirection: 'row',
    gap: '34px',
    width: '100%'
  }),
  skillListColumn: () => ({
    display: 'flex',
    flexDirection: 'column',
    gap: '4px'
  }),
  skillName: (palette, location) => {
    const theme = getColorTheme(palette, location)
    return {
      fontSize: '11px',
      lineHeight: '14px',
      fontWeight: '400',
      color: theme?.mainText,
      letterSpacing: '-0.11px',
      textTransform: 'capitalize',
      flex: '1',
      minWidth: '0',
      wordWrap: 'break-word'
    }
  },
  title: (palette, location) => {
    const theme = getColorTheme(palette, location)
    return {
      fontSize: '11px',
      lineHeight: '14px',
      fontWeight: '600',
      color: theme?.mainText,
      letterSpacing: '-0.11px'
    }
  },
  subTitle: (palette, location) => {
    const theme = getColorTheme(palette, location)
    return {
      fontSize: '11px',
      lineHeight: '14px',
      fontWeight: '400',
      color: theme?.mainText,
      letterSpacing: '-0.11px'
    }
  },
  personalInfoContainer: () => ({
    display: 'flex',
    flexDirection: 'column',
    marginBottom: '32px'
  }),
  sectionTop: () => ({
    display: 'flex',
    flexDirection: 'column',
    marginBottom: '10px'
  }),
  sectionItems: () => ({
    display: 'flex',
    flexDirection: 'column',
    gap: '12px',
    flex: '1',
    width: '100%'
  }),
  sectionItem: () => ({
    display: 'flex',
    flexDirection: 'column'
  }),
  sectionContainer: (layout) => ({
    display: 'flex',
    flexDirection: layout === resumeOptionValue.fullSplit ? 'row' : 'column',
    alignItems: 'flex-start',
    gap: '8px'
  }),
  pageNumber: (layout, palette) => {
    const isSidebarLayout =
      layout === resumeOptionValue.sidebarLeft ||
      layout === resumeOptionValue.sidebarRight
    const theme = getColorTheme(palette, isSidebarLayout ? 'sidebar' : 'main')

    return {
      position: 'absolute',
      bottom: '24px',
      [layout === resumeOptionValue.sidebarLeft ? 'left' : 'right']: '24px',
      color: theme?.mainText,
      fontSize: '9px',
      fontWeight: '400'
    }
  }
}

const defaultDescription = {
  root: {
    type: 'root',
    direction: 'ltr',
    format: '',
    indent: 0,
    version: 1,
    children: [
      {
        children: [
          {
            detail: 0,
            format: 0,
            mode: 'normal',
            style: '',
            text: '',
            type: 'text',
            version: 1
          }
        ],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'paragraph',
        textFormat: 0,
        version: 1
      }
    ]
  }
}

export const newExperience = {
  title: '',
  subTitle: '',
  isInternship: false,
  startMonth: '',
  startYear: '',
  endMonth: '',
  endYear: '',
  isPresent: false,
  description: defaultDescription,
  isVisible: true
}

export const newEducation = {
  title: '',
  subTitle: '',
  startMonth: '',
  startYear: '',
  endMonth: '',
  endYear: '',
  isPresent: false,
  description: defaultDescription,
  isVisible: true
}

export const newProficiencies = {
  title: '',
  subTitle: '',
  startMonth: '',
  startYear: '',
  showDate: false,
  description: defaultDescription,
  isVisible: true
}

export const newCustomSection = {
  title: '',
  subTitle: '',
  startMonth: '',
  startYear: '',
  showDate: false,
  description: defaultDescription,
  isVisible: true
}

export const newSocialLink = {
  url: '',
  name: ''
}

export const commonSchema = {
  title: z.string().optional(),
  subTitle: z.string().optional(),
  description: z.any().optional(),
  isVisible: z.boolean().optional().default(true),
  startMonth: z.string().optional(),
  startYear: z.string().optional(),
  endMonth: z.string().optional(),
  endYear: z.string().optional(),
  isPresent: z.boolean().optional().default(false),
  showDate: z.boolean().optional().default(false),
  isInternship: z.boolean().optional().default(false),
  skill: z.string(),
  rating: z.number().optional(),
  url: z.string().optional().default(''),
  name: z.string().optional().default(''),
  profileImage: z.any().optional(),
  showProfileImage: z.boolean().default(true),
  firstName: z.string(),
  lastName: z.string(),
  jobTitle: z.string(),
  summary: z.object(),
  phone: z.string(),
  email: z.string(),
  layout: z.string(),
  sidebarStyle: z.string(),
  spacing: z.string(),
  accent: z.string(),
  font: z.string(),
  headingStyle: z.string(),
  headingUnderlineStyle: z.string(),
  palette: z.string(),
  avatarStyle: z.string(),
  accentOptions: z.string().nullable(),
  backgroundPureWhite: z.boolean(),
  pageNumbers: z.boolean(),
  sections: z.array(z.any()).optional()
}

export const resumeItemSchemas = {
  link: z.object({
    url: commonSchema.url,
    name: commonSchema.name
  }),

  skill: z.object({
    skill: commonSchema.skill,
    rating: commonSchema.rating,
    isVisible: commonSchema.isVisible
  }),

  experience: z
    .object({
      title: commonSchema.title,
      subTitle: commonSchema.subTitle,
      isInternship: commonSchema.isInternship,
      startMonth: commonSchema.startMonth,
      startYear: commonSchema.startYear,
      endMonth: commonSchema.endMonth,
      endYear: commonSchema.endYear,
      isPresent: commonSchema.isPresent,
      description: commonSchema.description,
      isVisible: commonSchema.isVisible
    })
    .refine(validateDates, {
      message: 'endDateError',
      path: ['endYear']
    }),

  education: z
    .object({
      title: commonSchema.title,
      subTitle: commonSchema.subTitle,
      startMonth: commonSchema.startMonth,
      startYear: commonSchema.startYear,
      endMonth: commonSchema.endMonth,
      endYear: commonSchema.endYear,
      isPresent: commonSchema.isPresent,
      description: commonSchema.description,
      isVisible: commonSchema.isVisible
    })
    .refine(validateDates, {
      message: 'endDateError',
      path: ['endYear']
    }),

  proficiency: z.object({
    title: commonSchema.title,
    subTitle: commonSchema.subTitle,
    startMonth: commonSchema.startMonth,
    startYear: commonSchema.startYear,
    showDate: commonSchema.showDate,
    description: commonSchema.description,
    isVisible: commonSchema.isVisible
  }),

  customSection: z.object({
    title: commonSchema.title,
    subTitle: commonSchema.subTitle,
    startMonth: commonSchema.startMonth,
    startYear: commonSchema.startYear,
    showDate: commonSchema.showDate,
    description: commonSchema.description,
    isVisible: commonSchema.isVisible
  }),

  design: z.object({
    layout: commonSchema.layout,
    sidebarStyle: commonSchema.sidebarStyle,
    spacing: commonSchema.spacing,
    accent: commonSchema.accent,
    font: commonSchema.font,
    headingStyle: commonSchema.headingStyle,
    headingUnderlineStyle: commonSchema.headingUnderlineStyle,
    palette: commonSchema.palette,
    avatarStyle: commonSchema.avatarStyle,
    accentOptions: commonSchema.accentOptions,
    backgroundPureWhite: commonSchema.backgroundPureWhite,
    pageNumbers: commonSchema.pageNumbers
  })
}

export const resumeSectionSchemas = {
  links: z.array(resumeItemSchemas.link),
  experience: z.object({
    experience: z.array(resumeItemSchemas.experience)
  }),
  education: z.object({
    education: z.array(resumeItemSchemas.education)
  }),
  skills: z.array(resumeItemSchemas.skill),
  proficiencies: z.object({
    proficiencies: z.array(resumeItemSchemas.proficiency)
  }),
  customSections: z.object({
    customSections: z.array(resumeItemSchemas.customSection)
  })
}

export const personalDetailsSchema = z.object({
  profileImage: commonSchema.profileImage,
  showProfileImage: commonSchema.showProfileImage,
  firstName: commonSchema.firstName,
  lastName: commonSchema.lastName,
  jobTitle: commonSchema.jobTitle,
  summary: commonSchema.summary,
  phone: commonSchema.phone,
  email: commonSchema.email,
  links: resumeSectionSchemas.links
})

export const designSchema = z.object({
  layout: commonSchema.layout,
  sidebarStyle: commonSchema.sidebarStyle,
  spacing: commonSchema.spacing,
  accent: commonSchema.accent,
  font: commonSchema.font,
  headingStyle: commonSchema.headingStyle,
  headingUnderlineStyle: commonSchema.headingUnderlineStyle,
  palette: commonSchema.palette,
  avatarStyle: commonSchema.avatarStyle,
  accentOptions: commonSchema.accentOptions,
  backgroundPureWhite: commonSchema.backgroundPureWhite,
  pageNumbers: commonSchema.pageNumbers,
  sections: commonSchema.sections
})

export const resumeValues = {
  personalDetails: (data) => {
    return {
      profileImage: data?.profileImage || null,
      showProfileImage: data?.showProfileImage ?? true,
      firstName: data?.firstName || '',
      lastName: data?.lastName || '',
      jobTitle: data?.jobTitle || '',
      summary: data?.summary || defaultDescription,
      phone: data?.phone || '',
      email: data?.email || '',
      links: resumeValues.links(data)
    }
  },
  links: (data) => {
    return data?.links?.map(removeId) || [newSocialLink]
  },
  experience: (data, key) => {
    const sectionData = data?.experience?.map(removeId) || []
    return key ? { [key]: sectionData } : sectionData
  },
  education: (data, key) => {
    const sectionData = data?.education?.map(removeId) || []
    return key ? { [key]: sectionData } : sectionData
  },

  proficiencies: (data, key) => {
    const sectionData = data?.proficiencies?.map(removeId) || []
    return key ? { [key]: sectionData } : sectionData
  },
  customSections: (data, key) => {
    const sectionData = data?.customSections?.map(removeId) || []
    return key ? { [key]: sectionData } : sectionData
  },

  design: (data, key) => {
    return key ? { [key]: data } : data?.design
  }
}
