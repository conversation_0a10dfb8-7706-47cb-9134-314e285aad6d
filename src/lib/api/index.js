import { payload } from '@constants'
import { client } from '@lib/api-sdk/services.gen'

client.setConfig({
  baseUrl: process.env.NEXT_PUBLIC_URL
})

const isServer = typeof window === 'undefined'

if (isServer) {
  import('next/headers').then((module) => {
    const cookies = module.cookies

    client.interceptors.request.use(async (request) => {
      const cookieStore = await cookies()
      const token = cookieStore.get(payload.authTokenId)?.value

      // Set the Authorization header if the token exists
      if (token) {
        request.headers.set('Authorization', `Bearer ${token}`)
      }

      return request
    })
  })
}

client.interceptors.request.use(async (request) => {
  request.headers.set('x-swift', true)

  return request
})

export * as api from '@lib/api-sdk/services.gen'
