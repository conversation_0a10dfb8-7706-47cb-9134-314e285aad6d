// This file is auto-generated by @hey-api/openapi-ts

export type FindUsersResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindUsersError = unknown;

export type CreateUserData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreateUserResponse = (unknown);

export type CreateUserError = unknown;

export type UpdateUsersData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdateUsersResponse = (unknown);

export type UpdateUsersError = unknown;

export type DeleteUsersResponse = (void);

export type DeleteUsersError = unknown;

export type FindUserByIdData = {
    path: {
        id: string;
    };
};

export type FindUserByIdResponse = (unknown);

export type FindUserByIdError = unknown;

export type UpdateUserByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdateUserByIdResponse = (unknown);

export type UpdateUserByIdError = unknown;

export type DeleteUserByIdData = {
    path: {
        id: string;
    };
};

export type DeleteUserByIdResponse = (void);

export type DeleteUserByIdError = unknown;

export type CountUsersResponse = ({
    totalDocs?: number;
});

export type CountUsersError = unknown;

export type LoginResponse = ({
    [key: string]: unknown;
});

export type LoginError = unknown;

export type LogoutResponse = ({
    [key: string]: unknown;
});

export type LogoutError = unknown;

export type UnlockData = {
    body?: {
        email?: unknown;
    };
};

export type UnlockResponse = ({
    [key: string]: unknown;
});

export type UnlockError = unknown;

export type RefreshTokenResponse = ({
    [key: string]: unknown;
});

export type RefreshTokenError = unknown;

export type CurrentUserResponse = ({
    [key: string]: unknown;
});

export type CurrentUserError = unknown;

export type ForgotPasswordData = {
    body?: {
        email?: unknown;
    };
};

export type ForgotPasswordResponse = ({
    [key: string]: unknown;
});

export type ForgotPasswordError = unknown;

export type ResetPasswordData = {
    body?: {
        token?: unknown;
        password?: unknown;
    };
};

export type ResetPasswordResponse = ({
    [key: string]: unknown;
});

export type ResetPasswordError = unknown;

export type VerifyTokenResponse = ({
    [key: string]: unknown;
});

export type VerifyTokenError = unknown;

export type FindMediaResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindMediaError = unknown;

export type CreateMediaData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreateMediaResponse = (unknown);

export type CreateMediaError = unknown;

export type UpdateMediaData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdateMediaResponse = (unknown);

export type UpdateMediaError = unknown;

export type DeleteMediaResponse = (void);

export type DeleteMediaError = unknown;

export type FindMediaByIdData = {
    path: {
        id: string;
    };
};

export type FindMediaByIdResponse = (unknown);

export type FindMediaByIdError = unknown;

export type UpdateMediaByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdateMediaByIdResponse = (unknown);

export type UpdateMediaByIdError = unknown;

export type DeleteMediaByIdData = {
    path: {
        id: string;
    };
};

export type DeleteMediaByIdResponse = (void);

export type DeleteMediaByIdError = unknown;

export type CountMediaResponse = ({
    totalDocs?: number;
});

export type CountMediaError = unknown;

export type FindTemplatesResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindTemplatesError = unknown;

export type CreateTemplateData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreateTemplateResponse = (unknown);

export type CreateTemplateError = unknown;

export type UpdateTemplatesData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdateTemplatesResponse = (unknown);

export type UpdateTemplatesError = unknown;

export type DeleteTemplatesResponse = (void);

export type DeleteTemplatesError = unknown;

export type FindTemplateByIdData = {
    path: {
        id: string;
    };
};

export type FindTemplateByIdResponse = (unknown);

export type FindTemplateByIdError = unknown;

export type UpdateTemplateByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdateTemplateByIdResponse = (unknown);

export type UpdateTemplateByIdError = unknown;

export type DeleteTemplateByIdData = {
    path: {
        id: string;
    };
};

export type DeleteTemplateByIdResponse = (void);

export type DeleteTemplateByIdError = unknown;

export type CountTemplatesResponse = ({
    totalDocs?: number;
});

export type CountTemplatesError = unknown;

export type FindResumesResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindResumesError = unknown;

export type CreateResumeData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreateResumeResponse = (unknown);

export type CreateResumeError = unknown;

export type UpdateResumesData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdateResumesResponse = (unknown);

export type UpdateResumesError = unknown;

export type DeleteResumesResponse = (void);

export type DeleteResumesError = unknown;

export type FindResumeByIdData = {
    path: {
        id: string;
    };
};

export type FindResumeByIdResponse = (unknown);

export type FindResumeByIdError = unknown;

export type UpdateResumeByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdateResumeByIdResponse = (unknown);

export type UpdateResumeByIdError = unknown;

export type DeleteResumeByIdData = {
    path: {
        id: string;
    };
};

export type DeleteResumeByIdResponse = (void);

export type DeleteResumeByIdError = unknown;

export type CountResumesResponse = ({
    totalDocs?: number;
});

export type CountResumesError = unknown;

export type FindResumeExamplesResponse = ({
    docs?: unknown[];
    totalDocs?: number;
    limit?: number;
    totalPages?: number;
    page?: number;
    pagingCounter?: number;
    hasPrevPage?: boolean;
    hasNextPage?: boolean;
    prevPage?: (number) | null;
    nextPage?: (number) | null;
});

export type FindResumeExamplesError = unknown;

export type CreateResumeExampleData = {
    body?: {
        [key: string]: unknown;
    };
};

export type CreateResumeExampleResponse = (unknown);

export type CreateResumeExampleError = unknown;

export type UpdateResumeExamplesData = {
    body?: {
        [key: string]: unknown;
    };
};

export type UpdateResumeExamplesResponse = (unknown);

export type UpdateResumeExamplesError = unknown;

export type DeleteResumeExamplesResponse = (void);

export type DeleteResumeExamplesError = unknown;

export type FindResumeExampleByIdData = {
    path: {
        id: string;
    };
};

export type FindResumeExampleByIdResponse = (unknown);

export type FindResumeExampleByIdError = unknown;

export type UpdateResumeExampleByIdData = {
    body?: {
        [key: string]: unknown;
    };
    path: {
        id: string;
    };
};

export type UpdateResumeExampleByIdResponse = (unknown);

export type UpdateResumeExampleByIdError = unknown;

export type DeleteResumeExampleByIdData = {
    path: {
        id: string;
    };
};

export type DeleteResumeExampleByIdResponse = (void);

export type DeleteResumeExampleByIdError = unknown;

export type CountResumeExamplesResponse = ({
    totalDocs?: number;
});

export type CountResumeExamplesError = unknown;