// This file is auto-generated by @hey-api/openapi-ts

import { createClient, createConfig, type Options } from '@hey-api/client-fetch';
import type { FindUsersError, FindUsersResponse, CreateUserData, CreateUserError, CreateUserResponse, UpdateUsersData, UpdateUsersError, UpdateUsersResponse, DeleteUsersError, DeleteUsersResponse, FindUserByIdData, FindUserByIdError, FindUserByIdResponse, UpdateUserByIdData, UpdateUserByIdError, UpdateUserByIdResponse, DeleteUserByIdData, DeleteUserByIdError, DeleteUserByIdResponse, CountUsersError, CountUsersResponse, LoginError, LoginResponse, LogoutError, LogoutResponse, UnlockData, UnlockError, UnlockResponse, RefreshTokenError, RefreshTokenResponse, CurrentUserError, CurrentUserResponse, ForgotPasswordData, ForgotPasswordError, ForgotPasswordResponse, ResetPasswordData, ResetPasswordError, ResetPasswordResponse, VerifyTokenError, VerifyTokenResponse, FindMediaError, FindMediaResponse, CreateMediaData, CreateMediaError, CreateMediaResponse, UpdateMediaData, UpdateMediaError, UpdateMediaResponse, DeleteMediaError, DeleteMediaResponse, FindMediaByIdData, FindMediaByIdError, FindMediaByIdResponse, UpdateMediaByIdData, UpdateMediaByIdError, UpdateMediaByIdResponse, DeleteMediaByIdData, DeleteMediaByIdError, DeleteMediaByIdResponse, CountMediaError, CountMediaResponse, FindTemplatesError, FindTemplatesResponse, CreateTemplateData, CreateTemplateError, CreateTemplateResponse, UpdateTemplatesData, UpdateTemplatesError, UpdateTemplatesResponse, DeleteTemplatesError, DeleteTemplatesResponse, FindTemplateByIdData, FindTemplateByIdError, FindTemplateByIdResponse, UpdateTemplateByIdData, UpdateTemplateByIdError, UpdateTemplateByIdResponse, DeleteTemplateByIdData, DeleteTemplateByIdError, DeleteTemplateByIdResponse, CountTemplatesError, CountTemplatesResponse, FindResumesError, FindResumesResponse, CreateResumeData, CreateResumeError, CreateResumeResponse, UpdateResumesData, UpdateResumesError, UpdateResumesResponse, DeleteResumesError, DeleteResumesResponse, FindResumeByIdData, FindResumeByIdError, FindResumeByIdResponse, UpdateResumeByIdData, UpdateResumeByIdError, UpdateResumeByIdResponse, DeleteResumeByIdData, DeleteResumeByIdError, DeleteResumeByIdResponse, CountResumesError, CountResumesResponse, FindResumeExamplesError, FindResumeExamplesResponse, CreateResumeExampleData, CreateResumeExampleError, CreateResumeExampleResponse, UpdateResumeExamplesData, UpdateResumeExamplesError, UpdateResumeExamplesResponse, DeleteResumeExamplesError, DeleteResumeExamplesResponse, FindResumeExampleByIdData, FindResumeExampleByIdError, FindResumeExampleByIdResponse, UpdateResumeExampleByIdData, UpdateResumeExampleByIdError, UpdateResumeExampleByIdResponse, DeleteResumeExampleByIdData, DeleteResumeExampleByIdError, DeleteResumeExampleByIdResponse, CountResumeExamplesError, CountResumeExamplesResponse } from './types.gen';

export const client = createClient(createConfig());

/**
 * List all users
 */
export const findUsers = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindUsersResponse, FindUsersError, ThrowOnError>({
        ...options,
        url: '/api/users'
    });
};

/**
 * Create a new user
 */
export const createUser = <ThrowOnError extends boolean = false>(options?: Options<CreateUserData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateUserResponse, CreateUserError, ThrowOnError>({
        ...options,
        url: '/api/users'
    });
};

/**
 * Update users
 */
export const updateUsers = <ThrowOnError extends boolean = false>(options?: Options<UpdateUsersData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateUsersResponse, UpdateUsersError, ThrowOnError>({
        ...options,
        url: '/api/users'
    });
};

/**
 * Delete users
 */
export const deleteUsers = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteUsersResponse, DeleteUsersError, ThrowOnError>({
        ...options,
        url: '/api/users'
    });
};

/**
 * Retrieve a user by ID
 */
export const findUserById = <ThrowOnError extends boolean = false>(options: Options<FindUserByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindUserByIdResponse, FindUserByIdError, ThrowOnError>({
        ...options,
        url: '/api/users/{id}'
    });
};

/**
 * Update a user by ID
 */
export const updateUserById = <ThrowOnError extends boolean = false>(options: Options<UpdateUserByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateUserByIdResponse, UpdateUserByIdError, ThrowOnError>({
        ...options,
        url: '/api/users/{id}'
    });
};

/**
 * Delete a user by ID
 */
export const deleteUserById = <ThrowOnError extends boolean = false>(options: Options<DeleteUserByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteUserByIdResponse, DeleteUserByIdError, ThrowOnError>({
        ...options,
        url: '/api/users/{id}'
    });
};

/**
 * Count of users
 */
export const countUsers = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountUsersResponse, CountUsersError, ThrowOnError>({
        ...options,
        url: '/api/users/count'
    });
};

/**
 * Login
 */
export const login = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).post<LoginResponse, LoginError, ThrowOnError>({
        ...options,
        url: '/api/users/login'
    });
};

/**
 * Logout
 */
export const logout = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).post<LogoutResponse, LogoutError, ThrowOnError>({
        ...options,
        url: '/api/users/logout'
    });
};

/**
 * Unlock
 */
export const unlock = <ThrowOnError extends boolean = false>(options?: Options<UnlockData, ThrowOnError>) => {
    return (options?.client ?? client).post<UnlockResponse, UnlockError, ThrowOnError>({
        ...options,
        url: '/api/users/unlock'
    });
};

/**
 * Refresh token
 */
export const refreshToken = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).post<RefreshTokenResponse, RefreshTokenError, ThrowOnError>({
        ...options,
        url: '/api/users/refresh-token'
    });
};

/**
 * Current user
 */
export const currentUser = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CurrentUserResponse, CurrentUserError, ThrowOnError>({
        ...options,
        url: '/api/users/me'
    });
};

/**
 * Forgot password
 */
export const forgotPassword = <ThrowOnError extends boolean = false>(options?: Options<ForgotPasswordData, ThrowOnError>) => {
    return (options?.client ?? client).post<ForgotPasswordResponse, ForgotPasswordError, ThrowOnError>({
        ...options,
        url: '/api/users/forgot-password'
    });
};

/**
 * Reset password
 */
export const resetPassword = <ThrowOnError extends boolean = false>(options?: Options<ResetPasswordData, ThrowOnError>) => {
    return (options?.client ?? client).post<ResetPasswordResponse, ResetPasswordError, ThrowOnError>({
        ...options,
        url: '/api/users/reset-password'
    });
};

/**
 * Verify token
 */
export const verifyToken = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).post<VerifyTokenResponse, VerifyTokenError, ThrowOnError>({
        ...options,
        url: '/api/users/verify/{token}'
    });
};

/**
 * List all media
 */
export const findMedia = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindMediaResponse, FindMediaError, ThrowOnError>({
        ...options,
        url: '/api/media'
    });
};

/**
 * Create a new media
 */
export const createMedia = <ThrowOnError extends boolean = false>(options?: Options<CreateMediaData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateMediaResponse, CreateMediaError, ThrowOnError>({
        ...options,
        url: '/api/media'
    });
};

/**
 * Update media
 */
export const updateMedia = <ThrowOnError extends boolean = false>(options?: Options<UpdateMediaData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateMediaResponse, UpdateMediaError, ThrowOnError>({
        ...options,
        url: '/api/media'
    });
};

/**
 * Delete media
 */
export const deleteMedia = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteMediaResponse, DeleteMediaError, ThrowOnError>({
        ...options,
        url: '/api/media'
    });
};

/**
 * Retrieve a media by ID
 */
export const findMediaById = <ThrowOnError extends boolean = false>(options: Options<FindMediaByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindMediaByIdResponse, FindMediaByIdError, ThrowOnError>({
        ...options,
        url: '/api/media/{id}'
    });
};

/**
 * Update a media by ID
 */
export const updateMediaById = <ThrowOnError extends boolean = false>(options: Options<UpdateMediaByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateMediaByIdResponse, UpdateMediaByIdError, ThrowOnError>({
        ...options,
        url: '/api/media/{id}'
    });
};

/**
 * Delete a media by ID
 */
export const deleteMediaById = <ThrowOnError extends boolean = false>(options: Options<DeleteMediaByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteMediaByIdResponse, DeleteMediaByIdError, ThrowOnError>({
        ...options,
        url: '/api/media/{id}'
    });
};

/**
 * Count of media
 */
export const countMedia = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountMediaResponse, CountMediaError, ThrowOnError>({
        ...options,
        url: '/api/media/count'
    });
};

/**
 * List all templates
 */
export const findTemplates = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindTemplatesResponse, FindTemplatesError, ThrowOnError>({
        ...options,
        url: '/api/templates'
    });
};

/**
 * Create a new template
 */
export const createTemplate = <ThrowOnError extends boolean = false>(options?: Options<CreateTemplateData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateTemplateResponse, CreateTemplateError, ThrowOnError>({
        ...options,
        url: '/api/templates'
    });
};

/**
 * Update templates
 */
export const updateTemplates = <ThrowOnError extends boolean = false>(options?: Options<UpdateTemplatesData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateTemplatesResponse, UpdateTemplatesError, ThrowOnError>({
        ...options,
        url: '/api/templates'
    });
};

/**
 * Delete templates
 */
export const deleteTemplates = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteTemplatesResponse, DeleteTemplatesError, ThrowOnError>({
        ...options,
        url: '/api/templates'
    });
};

/**
 * Retrieve a template by ID
 */
export const findTemplateById = <ThrowOnError extends boolean = false>(options: Options<FindTemplateByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindTemplateByIdResponse, FindTemplateByIdError, ThrowOnError>({
        ...options,
        url: '/api/templates/{id}'
    });
};

/**
 * Update a template by ID
 */
export const updateTemplateById = <ThrowOnError extends boolean = false>(options: Options<UpdateTemplateByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateTemplateByIdResponse, UpdateTemplateByIdError, ThrowOnError>({
        ...options,
        url: '/api/templates/{id}'
    });
};

/**
 * Delete a template by ID
 */
export const deleteTemplateById = <ThrowOnError extends boolean = false>(options: Options<DeleteTemplateByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteTemplateByIdResponse, DeleteTemplateByIdError, ThrowOnError>({
        ...options,
        url: '/api/templates/{id}'
    });
};

/**
 * Count of templates
 */
export const countTemplates = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountTemplatesResponse, CountTemplatesError, ThrowOnError>({
        ...options,
        url: '/api/templates/count'
    });
};

/**
 * List all resumes
 */
export const findResumes = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindResumesResponse, FindResumesError, ThrowOnError>({
        ...options,
        url: '/api/resumes'
    });
};

/**
 * Create a new resume
 */
export const createResume = <ThrowOnError extends boolean = false>(options?: Options<CreateResumeData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateResumeResponse, CreateResumeError, ThrowOnError>({
        ...options,
        url: '/api/resumes'
    });
};

/**
 * Update resumes
 */
export const updateResumes = <ThrowOnError extends boolean = false>(options?: Options<UpdateResumesData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateResumesResponse, UpdateResumesError, ThrowOnError>({
        ...options,
        url: '/api/resumes'
    });
};

/**
 * Delete resumes
 */
export const deleteResumes = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteResumesResponse, DeleteResumesError, ThrowOnError>({
        ...options,
        url: '/api/resumes'
    });
};

/**
 * Retrieve a resume by ID
 */
export const findResumeById = <ThrowOnError extends boolean = false>(options: Options<FindResumeByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindResumeByIdResponse, FindResumeByIdError, ThrowOnError>({
        ...options,
        url: '/api/resumes/{id}'
    });
};

/**
 * Update a resume by ID
 */
export const updateResumeById = <ThrowOnError extends boolean = false>(options: Options<UpdateResumeByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateResumeByIdResponse, UpdateResumeByIdError, ThrowOnError>({
        ...options,
        url: '/api/resumes/{id}'
    });
};

/**
 * Delete a resume by ID
 */
export const deleteResumeById = <ThrowOnError extends boolean = false>(options: Options<DeleteResumeByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteResumeByIdResponse, DeleteResumeByIdError, ThrowOnError>({
        ...options,
        url: '/api/resumes/{id}'
    });
};

/**
 * Count of resumes
 */
export const countResumes = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountResumesResponse, CountResumesError, ThrowOnError>({
        ...options,
        url: '/api/resumes/count'
    });
};

/**
 * List all resume examples
 */
export const findResumeExamples = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<FindResumeExamplesResponse, FindResumeExamplesError, ThrowOnError>({
        ...options,
        url: '/api/resume-examples'
    });
};

/**
 * Create a new resume example
 */
export const createResumeExample = <ThrowOnError extends boolean = false>(options?: Options<CreateResumeExampleData, ThrowOnError>) => {
    return (options?.client ?? client).post<CreateResumeExampleResponse, CreateResumeExampleError, ThrowOnError>({
        ...options,
        url: '/api/resume-examples'
    });
};

/**
 * Update resume examples
 */
export const updateResumeExamples = <ThrowOnError extends boolean = false>(options?: Options<UpdateResumeExamplesData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateResumeExamplesResponse, UpdateResumeExamplesError, ThrowOnError>({
        ...options,
        url: '/api/resume-examples'
    });
};

/**
 * Delete resume examples
 */
export const deleteResumeExamples = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteResumeExamplesResponse, DeleteResumeExamplesError, ThrowOnError>({
        ...options,
        url: '/api/resume-examples'
    });
};

/**
 * Retrieve a resume example by ID
 */
export const findResumeExampleById = <ThrowOnError extends boolean = false>(options: Options<FindResumeExampleByIdData, ThrowOnError>) => {
    return (options?.client ?? client).get<FindResumeExampleByIdResponse, FindResumeExampleByIdError, ThrowOnError>({
        ...options,
        url: '/api/resume-examples/{id}'
    });
};

/**
 * Update a resume example by ID
 */
export const updateResumeExampleById = <ThrowOnError extends boolean = false>(options: Options<UpdateResumeExampleByIdData, ThrowOnError>) => {
    return (options?.client ?? client).patch<UpdateResumeExampleByIdResponse, UpdateResumeExampleByIdError, ThrowOnError>({
        ...options,
        url: '/api/resume-examples/{id}'
    });
};

/**
 * Delete a resume example by ID
 */
export const deleteResumeExampleById = <ThrowOnError extends boolean = false>(options: Options<DeleteResumeExampleByIdData, ThrowOnError>) => {
    return (options?.client ?? client).delete<DeleteResumeExampleByIdResponse, DeleteResumeExampleByIdError, ThrowOnError>({
        ...options,
        url: '/api/resume-examples/{id}'
    });
};

/**
 * Count of resume examples
 */
export const countResumeExamples = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => {
    return (options?.client ?? client).get<CountResumeExamplesResponse, CountResumeExamplesError, ThrowOnError>({
        ...options,
        url: '/api/resume-examples/count'
    });
};