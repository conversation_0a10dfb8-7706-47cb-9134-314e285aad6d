export const detectOrphanedSections = (pages) => {
  const orphanedSections = []

  for (const [pageIndex, page] of pages.entries()) {
    const pageContainer = document.createElement('div')
    pageContainer.innerHTML = page.contentOnly

    const headings = pageContainer.querySelectorAll('.smart-break-target')

    for (const heading of headings) {
      const smartClass = [...heading.classList].find((cls) =>
        cls.startsWith('smart-section-')
      )

      if (smartClass) {
        const content = heading.nextElementSibling

        let isOrphaned = false
        const hasContentText = content?.textContent?.trim()?.length > 0

        if (!content || !content.classList.contains('inner-content') || !hasContentText) {
          isOrphaned = true
        } else {
          const visibleChildren = [...content.children].filter(
            (child) => !('undisplayed' in child.dataset)
          )
          isOrphaned = visibleChildren.length === 0
        }

        if (isOrphaned) {
          orphanedSections.push({
            pageIndex,
            smartClass
          })
        }
      }
    }
  }

  return orphanedSections
}
