import { VirtualPreviewer } from './VirtualPreviewer'

export const renderPagesContentOnly = async (
  content,
  stylesheets = [],
  width,
  height,
  options = {}
) => {
  const customPageCSS = `
  @page {
    size: ${width}px ${height - (options.offset || 0)}px;
    margin: 0;
  }`

  const enhancedStylesheets = [...stylesheets, { 'custom-dimensions': customPageCSS }]

  const previewer = new VirtualPreviewer(options)
  const flow = await previewer.preview(content, enhancedStylesheets)

  const result = {
    pages: flow.pages.map((page, index) => {
      const pageContent = page.element.querySelector('.pagedjs_page_content')
      const actualDimensions = pageContent
        ? {
            width: Math.round(pageContent.getBoundingClientRect().width),
            height: Math.round(pageContent.getBoundingClientRect().height)
          }
        : { width: 'unknown', height: 'unknown' }

      return {
        id: page.id,
        index: index,
        content: page.element.outerHTML,
        textContent: page.element.textContent,
        element: page.element.cloneNode(true),
        dimensions: { width, height },
        actualContentDimensions: actualDimensions
      }
    }),
    total: flow.total,
    performance: flow.performance,
    size: flow.size,
    flow: flow,
    customDimensions: { width, height }
  }

  // Clean up the previewer and any hidden containers
  try {
    if (previewer && typeof previewer.destroy === 'function') {
      previewer.destroy()
      //console.log('Previewer destroyed and hidden containers cleaned up')
    }
  } catch (error) {
    console.warn('Error during previewer cleanup:', error)
  }

  return {
    ...result,
    pages: result.pages.map((page) => {
      const pageContent = page.element.querySelector('.content-root')
      const contentOnly = pageContent ? pageContent.outerHTML : page.content

      return {
        ...page,
        contentOnly: contentOnly
      }
    })
  }
}
