import { Handler } from 'pagedjs'
import { resumeOptionValue, resumeStyles } from '@utils'

export class LayoutFixHandler extends Handler {
  constructor(chunker, polisher, caller) {
    super(chunker, polisher, caller)
  }

  afterPageLayout(pageElement) {
    // Target only main sections that use two-column layout
    const mainSections = pageElement.querySelectorAll('.main-section.section-wrapper')

    for (const section of mainSections) {
      const firstChild = section.firstElementChild
      const hasHeading = firstChild && firstChild.classList.contains('section-heading')
      const hasContent = section.querySelector('.section-content.inner-content')

      const sectionTitleStyle = resumeStyles.sectionTitle(resumeOptionValue.fullSplit)

      if (!hasHeading && hasContent) {
        const placeholder = document.createElement('div')
        placeholder.className = 'section-heading'
        Object.assign(placeholder.style, sectionTitleStyle)
        section.insertBefore(placeholder, section.firstChild)
      }
    }
  }
}
