import { Previewer, registerHandlers } from 'pagedjs'
import { LayoutFixHandler } from './LayoutFixHandler'
import { VirtualChunker } from './VirtualChunker'

// Register the layout fix handler globally - runs once when module is imported
registerHandlers(LayoutFixHandler)

export class VirtualPreviewer extends Previewer {
  constructor(options = {}) {
    super(options)

    this.chunker = new VirtualChunker(undefined, undefined, this.settings)

    this.chunker.on('page', (page) => {
      this.emit('page', page)
    })

    this.chunker.on('rendering', () => {
      this.emit('rendering', this.chunker)
    })
  }

  destroy() {
    if (this.chunker && typeof this.chunker.destroy === 'function') {
      this.chunker.destroy()
    }

    if (super.destroy && typeof super.destroy === 'function') {
      super.destroy()
    }
  }
}
