import { addPageBreaksToHtml } from './addPageBreaksToHtml'
import { detectOrphanedSections } from './detectOrphanedSections'
import { renderPagesContentOnly } from './renderPagesContentOnly'

export const renderPagesWithSmartBreaks = async (
  content,
  stylesheets = [],
  width,
  height,
  options = {}
) => {
  const maxIterations = 5
  let currentHTML = content
  let iteration = 0
  let dynamicPageBreakCSS = ''

  while (iteration < maxIterations) {
    const enhancedStylesheets = dynamicPageBreakCSS
      ? [...stylesheets, { 'smart-page-breaks': dynamicPageBreakCSS }]
      : stylesheets

    const result = await renderPagesContentOnly(
      currentHTML,
      enhancedStylesheets,
      width,
      height,
      options
    )

    const orphanedSections = detectOrphanedSections(result.pages)

    if (orphanedSections.length === 0) {
      return {
        ...result,
        smartBreaksApplied: iteration > 0,
        iterations: iteration + 1,
        orphanedSectionsFixed: iteration > 0 ? 'Yes' : 'None needed'
      }
    }

    const sectionToFix = orphanedSections.slice(0, 1)

    const modifiedHTML = addPageBreaksToHtml(currentHTML, sectionToFix)

    const cssRules = sectionToFix
      .map(
        ({ smartClass }) =>
          `.smart-break-target.${smartClass} { page-break-before: always !important; break-before: page !important; }`
      )
      .join('\n')

    if (cssRules) {
      dynamicPageBreakCSS = cssRules
    }

    if (modifiedHTML === currentHTML && !cssRules) {
      break
    }

    currentHTML = modifiedHTML
    iteration++
  }

  const finalResult = await renderPagesContentOnly(
    currentHTML,
    stylesheets,
    width,
    height,
    options
  )
  return {
    ...finalResult,
    smartBreaksApplied: true,
    iterations: maxIterations,
    maxIterationsReached: true,
    orphanedSectionsFixed: 'Partial (max iterations reached)',
    reliability: 'degraded'
  }
}
