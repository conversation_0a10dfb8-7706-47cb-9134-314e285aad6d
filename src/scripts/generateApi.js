import camelCase from 'camelcase'
import fs from 'node:fs'
import { collections } from '@constants'
import configPromise from '@payload-config'

const ignoreList = new Set([
  'payload-locked-documents',
  'payload-preferences',
  'payload-migrations',
  collections.admins.slug
])

// OpenAPI base document
const openApiDocument = {
  openapi: '3.0.0',
  info: {
    title: 'Swift Resume API',
    version: '1.0.0',
    description: 'Auto-generated OpenAPI document for the Swift Resume API.'
  },
  paths: {}
}

const processOperationId = (name) => {
  return camelCase(name)
}

// Function to convert Payload collections to OpenAPI paths
const convertCollectionToOpenApi = (collection) => {
  const { slug, fields, labels } = collection // eslint-disable-line no-unused-vars
  const { plural: pluralSentence, singular: singularSentence } = labels

  const plural = pluralSentence.toLowerCase()
  const singular = singularSentence.toLowerCase()

  const path = `/api/${slug}`

  openApiDocument.paths[path] = {
    // Find
    get: {
      summary: `List all ${plural}`,
      operationId: processOperationId(`find-${plural}`),
      tags: [slug],
      responses: {
        200: {
          description: `A list of ${plural}`,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  docs: {
                    type: 'array'
                    // TODO: Sort this!
                    // items: { type: 'object', properties: generateProperties(fields) }
                  },
                  totalDocs: { type: 'integer' },
                  limit: { type: 'integer' },
                  totalPages: { type: 'integer' },
                  page: { type: 'integer' },
                  pagingCounter: { type: 'integer' },
                  hasPrevPage: { type: 'boolean' },
                  hasNextPage: { type: 'boolean' },
                  prevPage: { type: 'integer', nullable: true },
                  nextPage: { type: 'integer', nullable: true }
                }
              }
            }
          }
        }
      }
    },
    // Create
    post: {
      summary: `Create a new ${singular}`,
      operationId: processOperationId(`create-${singular}`),
      tags: [slug],
      requestBody: {
        content: {
          'application/json': {
            schema: {
              type: 'object'
              // TODO: Sort this!
              // properties: generateProperties(fields)
            }
          }
        }
      },
      responses: {
        201: {
          description: `Created ${singular}`,
          content: {
            'application/json': {
              // TODO: Sort this!
              // schema: { type: 'object', properties: generateProperties(fields) }
            }
          }
        }
      }
    },
    // Update
    patch: {
      summary: `Update ${plural}`,
      operationId: processOperationId(`update-${plural}`),
      tags: [slug],
      requestBody: {
        content: {
          'application/json': {
            schema: {
              type: 'object'
              // TODO: Sort this!
              // properties: generateProperties(fields)
            }
          }
        }
      },
      responses: {
        200: {
          description: `Updated ${plural}`,
          content: {
            'application/json': {
              // TODO: Sort this!
              // schema: { type: 'object', properties: generateProperties(fields) }
            }
          }
        }
      }
    },
    // Delete
    delete: {
      summary: `Delete ${plural}`,
      operationId: processOperationId(`delete-${plural}`),
      tags: [slug],
      responses: {
        204: {
          description: `${plural} deleted successfully`
        }
      }
    }
  }

  // Find By ID, Update By ID, Delete By ID
  openApiDocument.paths[`${path}/{id}`] = {
    get: {
      summary: `Retrieve a ${singular} by ID`,
      operationId: processOperationId(`find-${singular}-by-id`),
      tags: [slug],
      parameters: [
        { name: 'id', in: 'path', required: true, schema: { type: 'string' } }
      ],
      responses: {
        200: {
          description: `A single ${singular}`,
          content: {
            'application/json': {
              // TODO: Sort this!
              // schema: { type: 'object', properties: generateProperties(fields) }
            }
          }
        }
      }
    },
    patch: {
      summary: `Update a ${singular} by ID`,
      operationId: processOperationId(`update-${singular}-by-id`),
      tags: [slug],
      parameters: [
        { name: 'id', in: 'path', required: true, schema: { type: 'string' } }
      ],
      requestBody: {
        content: {
          'application/json': {
            schema: {
              type: 'object'
              // TODO: Sort this!
              // properties: generateProperties(fields)
            }
          }
        }
      },
      responses: {
        200: {
          description: `Updated ${singular}`,
          content: {
            'application/json': {
              // TODO: Sort this!
              // schema: { type: 'object', properties: generateProperties(fields) }
            }
          }
        }
      }
    },
    delete: {
      summary: `Delete a ${singular} by ID`,
      operationId: processOperationId(`delete-${singular}-by-id`),
      tags: [slug],
      parameters: [
        { name: 'id', in: 'path', required: true, schema: { type: 'string' } }
      ],
      responses: {
        204: {
          description: `${singular} deleted`
        }
      }
    }
  }

  // Count path
  openApiDocument.paths[`${path}/count`] = {
    get: {
      summary: `Count of ${plural}`,
      operationId: processOperationId(`count-${plural}`),
      tags: [slug],
      responses: {
        200: {
          description: `Count of ${plural}`,
          content: {
            'application/json': {
              schema: { type: 'object', properties: { totalDocs: { type: 'integer' } } }
            }
          }
        }
      }
    }
  }

  if (collection.auth) {
    openApiDocument.paths[`${path}/login`] = generateAuthPath({
      summary: 'Login',
      method: 'POST',
      slug
    })

    openApiDocument.paths[`${path}/logout`] = generateAuthPath({
      summary: 'Logout',
      method: 'POST',
      slug
    })

    openApiDocument.paths[`${path}/unlock`] = generateAuthPath({
      summary: 'Unlock',
      method: 'POST',
      slug,
      requestBody: { email: 'string' }
    })

    openApiDocument.paths[`${path}/refresh-token`] = generateAuthPath({
      summary: 'Refresh token',
      method: 'POST',
      slug
    })

    openApiDocument.paths[`${path}/me`] = generateAuthPath({
      summary: 'Current user',
      method: 'GET',
      slug
    })

    openApiDocument.paths[`${path}/forgot-password`] = generateAuthPath({
      summary: 'Forgot password',
      method: 'POST',
      slug,
      requestBody: { email: 'string' }
    })

    openApiDocument.paths[`${path}/reset-password`] = generateAuthPath({
      summary: 'Reset password',
      method: 'POST',
      slug,
      requestBody: { token: 'string', password: 'string' }
    })

    openApiDocument.paths[`${path}/verify/{token}`] = generateAuthPath({
      summary: 'Verify token',
      method: 'POST',
      slug
    })
  }
}

// Function to generate auth-related paths
const generateAuthPath = ({ summary, method, requestBody = null }) => {
  const path = {
    [method.toLowerCase()]: {
      summary,
      operationId: processOperationId(summary),
      tags: ['auth'],
      responses: {
        200: {
          description: summary,
          content: {
            'application/json': {
              schema: { type: 'object' }
            }
          }
        }
      }
    }
  }

  // If request body is needed, add to the path definition
  if (requestBody) {
    path[method.toLowerCase()].requestBody = {
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: requestBody
          }
        }
      }
    }
  }

  return path
}

// const generateProperties = (fields) => {
//   const properties = {}

//   fields.forEach((field) => {
//     const fieldType = field.type
//     let openApiType

//     switch (fieldType) {
//       case 'text':
//       case 'title':
//       case 'slug':
//       case 'richText':
//       case 'textarea':
//       case 'code':
//         openApiType = 'string'
//         break

//       case 'boolean': // Checkbox, Radio group, and UI fields are booleans
//       case 'checkbox':
//       case 'radio':
//         openApiType = 'boolean'
//         break

//       case 'number':
//       case 'integer':
//       case 'point':
//         openApiType = 'integer'
//         break

//       case 'email':
//         openApiType = 'string'
//         properties[field.name].format = 'email'
//         break

//       case 'date':
//         openApiType = 'string'
//         properties[field.name].format = 'date'
//         break

//       case 'datetime':
//         openApiType = 'string'
//         properties[field.name].format = 'date-time'
//         break

//       case 'array':
//       case 'select':
//         openApiType = 'array'
//         properties[field.name].items = generateProperties(field.options) // Recursively handle array fields
//         break

//       case 'file':
//       case 'upload':
//         openApiType = 'string'
//         properties[field.name].format = 'uri'
//         break

//       case 'group':
//         openApiType = 'object'
//         properties[field.name].properties = generateProperties(field.fields) // Recursively handle nested groups
//         break

//       case 'relationship':
//         openApiType = 'object'
//         properties[field.name].properties = {
//           // Relationship can link to another collection (so it's an object with the related fields)
//           id: { type: 'string' } // Assuming a related item with a string ID
//         }
//         break

//       case 'join':
//         openApiType = 'array'
//         properties[field.name].items = {
//           type: 'object',
//           properties: {
//             id: { type: 'string' }
//           }
//         }
//         break

//       case 'tabs':
//         openApiType = 'array'
//         properties[field.name].items = { type: 'string' } // Tabs are usually just strings representing tab names
//         break

//       case 'row':
//         openApiType = 'array'
//         properties[field.name].items = { type: 'object' } // Row can be an array of objects (fields inside the row)
//         break

//       case 'collapsible':
//         openApiType = 'object'
//         properties[field.name].properties = generateProperties(field.fields) // Collapsible groups can have nested properties
//         break

//       default:
//         openApiType = 'string' // Default to string if no match
//         break
//     }

//     // Add the field to properties
//     properties[field.name] = {
//       type: openApiType,
//       ...(properties[field.name] || {})
//     }
//   })

//   return properties
// }

// Function to generate OpenAPI properties from fields
// const generateProperties = (fields) => {
//   const properties = {}

//   fields.forEach((field) => {
//     properties[field.name] = {
//       type: field.type === 'text' ? 'string' : field.type
//     }
//   })

//   return properties
// }

export const generateApi = async () => {
  const config = await configPromise

  const collections = config.collections.filter((c) => !ignoreList.has(c.slug))

  // Generate paths for each Payload collection
  for (const collection of collections) {
    convertCollectionToOpenApi(collection)
  }

  // Write OpenAPI document to JSON file
  fs.writeFileSync('openapi.json', JSON.stringify(openApiDocument, null, 2))
  console.log('OpenAPI document generated: openapi.json')
}
