import path from 'node:path'
import { fileURLToPath } from 'node:url'
import { buildConfig } from 'payload'
import sharp from 'sharp'
import { admins, media, resumeExamples, resumes, templates, users } from '@collections'
import { collections } from '@constants'
import { mongooseAdapter } from '@payloadcms/db-mongodb'
import { resendAdapter } from '@payloadcms/email-resend'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import { s3Storage } from '@payloadcms/storage-s3'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

const urls = [process.env.NEXT_PUBLIC_URL || '']

export default buildConfig({
  admin: {
    // @ts-ignore
    user: collections.admins.slug,
    importMap: {
      baseDir: path.resolve(dirname)
    }
  },
  onInit: async (payload) => {
    const existingAdmins = await payload.find({
      // @ts-ignore
      collection: collections.admins.slug,
      limit: 1
    })

    if (existingAdmins.docs.length === 0) {
      await payload.create({
        // @ts-ignore
        collection: collections.admins.slug,
        data: {
          email: '<EMAIL>',
          password: 'asdasdasd'
        }
      })
    }
  },
  // @ts-ignore
  collections: [admins, users, media, templates, resumes, resumeExamples],
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts')
  },
  db: mongooseAdapter({
    url: process.env.DATABASE_URI || ''
  }),
  plugins: [
    s3Storage({
      collections: {
        media: {
          disableLocalStorage: true,
          disablePayloadAccessControl: true,
          prefix: collections.media.uploadDir
        }
      },
      config: {
        forcePathStyle: false,
        credentials: {
          accessKeyId: process.env.DO_S3_ACCESS_KEY as string,
          secretAccessKey: process.env.DO_S3_SECRET_KEY as string
        },
        region: process.env.DO_S3_REGION as string,
        endpoint: process.env.DO_S3_ENDPOINT
      },
      bucket: process.env.DO_S3_BUCKET as string,
      acl: 'public-read'
    })
  ],
  serverURL: process.env.NEXT_PUBLIC_URL,
  cors: {
    origins: urls,
    headers: ['x-swift']
  },
  csrf: urls,
  sharp,
  email: resendAdapter({
    apiKey: process.env.RESEND_API_KEY || '',
    defaultFromAddress: process.env.RESEND_FROM_EMAIL || '',
    defaultFromName: process.env.RESEND_FROM_NAME || ''
  }),
  upload: {
    limits: {
      fileSize: collections.media.maxFileSize
    }
  }
})
