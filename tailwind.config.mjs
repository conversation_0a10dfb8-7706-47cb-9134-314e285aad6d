/** @type {import('tailwindcss').Config} */

const config = {
  darkMode: ['class'],
  content: ['./src/**/*.{js,ts,jsx,tsx,mdx}'],
  theme: {
    extend: {
      fontFamily: {
        sans: ['var(--font-inter)']
      },
      letterSpacing: {
        tightest: '0.9px',
        tighter: '0.4px',
        tight: '0.2px',
        normal: '0.1px'
      },
      lineHeight: {
        4.5: '18px'
      },
      colors: {
        primary: {
          700: '#3463CB',
          DEFAULT: '#2563eb'
        }
      },
      height: {
        7.5: '30px'
      },
      padding: {
        4.5: '18px'
      },
      maxWidth: {
        'screen-cn': '1300px'
      },
      boxShadow: {
        'btn-up':
          '0px 1px 2px 0px rgba(0, 0, 0, 0.15), 0px 0px 0px 1px rgba(255, 255, 255, 0.15) inset',
        'btn-out':
          '0px 0px 0px 2px rgba(255, 255, 255, 0.40) inset, 0px 1px 2px 0px rgba(0, 0, 0, 0.05)',
        'btn-flat': '0px 1px 2px 0px rgba(0, 0, 0, 0.05)',
        switch:
          '0px 4px 6px -4px rgba(0, 0, 0, 0.1), 0px 10px 15px -3px rgba(0, 0, 0, 0.1)',
        card: '1px 4px 20px 0px rgba(159, 160, 176, 0.1), 0px -1px 9px 0px rgba(153, 153, 153, 0.07), 0px 1px 0px 0px rgba(255, 255, 255, 0.35)',
        'resume-page': '0px 0px 12px 0px rgba(0, 0, 0, 0.05)'
      },
      backgroundImage: {
        'resume-card': 'linear-gradient(180deg, #F8FAFC 0%, #FFFFFF 70%)'
      },
      keyframes: {
        'accordion-down': {
          from: {
            height: '0'
          },
          to: {
            height: 'var(--radix-accordion-content-height)'
          }
        },
        'accordion-up': {
          from: {
            height: 'var(--radix-accordion-content-height)'
          },
          to: {
            height: '0'
          }
        }
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out'
      },
      aspectRatio: {
        'resume-image': '1.7/1'
      }
    }
  },
  plugins: [require('tailwindcss-animate')]
}

export default config
