import { spawn } from 'node:child_process'

let dockerProcess, devProcess

const startDocker = () => {
  console.log('🖥️ Starting Docker containers...')

  dockerProcess = spawn(
    'docker-compose',
    ['up', '--no-log-prefix', '--remove-orphans', '--detach'],
    {
      stdio: 'inherit',
      detached: true
    }
  )

  dockerProcess.on('error', (error) => {
    console.error('Error starting Docker:', error)
    stopAll()
  })
}

const startDev = () => {
  console.log('🧑‍💻 Starting Next.js dev server...')

  devProcess = spawn('pnpm', ['run', 'start-dev'], {
    stdio: 'inherit',
    detached: true
  })

  devProcess.on('exit', (code) => {
    console.log(`Next.js server exited with code ${code}`)
    stopAll()
    process.exit(code) // eslint-disable-line unicorn/no-process-exit
  })

  devProcess.on('error', (error) => {
    console.error('Error starting Next.js:', error)
    stopAll()
  })
}

const stopAll = () => {
  console.log('✋ Stopping processes...')

  if (dockerProcess) {
    console.log('🖥️ Stopping Docker containers...')

    const stopDocker = spawn('docker-compose', ['down'], { stdio: 'inherit' })

    stopDocker.on('exit', () => {
      console.log('Docker containers stopped.')
    })
  }

  if (devProcess) {
    console.log('🧑‍💻 Stopping NextJS dev server...')

    try {
      process.kill(-devProcess.pid, 'SIGINT')
    } catch (error) {
      if (error.code !== 'ESRCH') {
        console.error('Error stopping NextJS dev server:', error)
      }
    }
  }
}

process.on('SIGINT', stopAll)
process.on('SIGTERM', stopAll)

// Start the processes
startDocker()
startDev()
