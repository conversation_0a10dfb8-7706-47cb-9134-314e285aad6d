## Getting started

First, you need to install:

- Node LTS (recommend using `nvm` → `nvm install --lts && nvm use --lts`)
- [Docker](https://docs.docker.com/engine/install)
- [pnpm](https://pnpm.io/installation)
- [Homebrew](https://brew.sh)

Then, clone the repo and:

- Copy the `.env.example` to a new `.env` file
- Follow instructions to setup HTTPS

## HTTPS setup

### Generate a local SSL certificate

- `brew install mkcert`
- `brew install nss`
- `cd` to the repo
- `cd nginx/certs` (`mkdir` if not there)
- `mkcert -install`
- `mkcert -key-file swift.local.key.pem -cert-file swift.local.cert.pem swift.local "*.swift.local"`
- Two new `.pem` files should now be in the `certs` folder

### Add hosts

Run the following:

Mac

```bash
echo -e "\n\n# Swift\n127.0.0.1         swift.local\n127.0.0.1         app.swift.local" | sudo tee -a /etc/hosts`
```

Windows

```Powershell
Add-Content -Path "C:\Windows\System32\drivers\etc\hosts" -Value "`n`n# Swift`n127.0.0.1         swift.local`n127.0.0.1         app.swift.local"
```

## Running the app

- run `pnpm install`
- run `pnpm dev`

Inside Docker, you will see the application and Mongo running in containers.

```
- https://swift.local ← Is the root
- https://swift.local/app ← Points to the `/app` folder in the router
- Example: https://swift.local/app/sign-up
```

Note: At some point we will likely move to app.swift.local subdomain, but the payload-token cookie issue needs to be figured out first.

Download [MongoDB Compass](https://www.mongodb.com/products/tools/compass) to explore the database in a nice UI.

## Package manager

This repo uses `pnpm`. You can read more about `pnpm` [here](https://pnpm.io/). Please don't use `npm` or `yarn`, as we don't want multiple lockfile types.

## Technology

This stack is built on top of NextJS and Payload CMS. Effectively, Payload handles the database documents (known as collections) and provides a nice CMS to edit this data. NextJS handles virtually everything else (bundling, routing, back-end, front-end and much more). The front-end UI framework is React.

## Folder structure

Here is the proposed folder structure for this application. Not all folders exist yet, but this is the direction to take. If in doubt, just push towards making progress and we can always reorganise later. This is generally aiming to follow the new NextJS 15 folder patterns, along with some Payload patterns and common sense.

```
src
├── access – Manage all access controls
├── actions – Server actions
├── app – NextJS app router
├── collections – Payload collections
├── components – React components
├── context – Data/state providers
├── hooks – Reusable hooks
├── lib – General purpose generated code
├── scripts – One-time scripts for startup
├── styles – global styling
├── utils – helper/utility functions
├── constants.js – constant variables
└── instrumentation.js – NextJS startup script
```

### More info

- [Payload access control](https://payloadcms.com/docs/access-control/overview)
- [Server actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations) and [React docs](https://react.dev/reference/rsc/server-actions)
- [Payload collections](https://payloadcms.com/docs/beta/configuration/collections)
- [Next instrumentation](https://nextjs.org/docs/app/building-your-application/optimizing/instrumentation)

## Linting and pre-commit

The code base is linted with ESLint and formatted with Prettier. It's recommended that you use the "format on save" feature in your editor.

The linting will also happen on app start and when you try to commit to git. Please don't ever force or skip these hooks. We only ever want linted code hitting the repo.

## Aliases and barrel files

To keep imports neat, there are some predefined path aliases in the `tsconfig.js`. Please aim to learn these and use them over relative paths (`../../../../` etc).

The only exception is if the file is in the same folder – then you can either use an alias or not, it's up to you.

You will also notice a lot of imports are formatted like this:

```js
import { isAdmin, isAnon } from '@access'
```

## How to add a new collection

- Add a new collection config file in `@collections`. Use an existing collection as a starting point
- Add the new collection to the `@payload-config` file
- Restart the app
- In another terminal run `pnpm run generate:api-sdk`, which will add the endpoints to the API client
- Alternatively if you don't want to expose this collection as an API, add the collection name to the `ignoreList` in `generateApi.js`

## The API

When you need to perform CRUD operations on the database collections, you should use the API client. The API client uses the Payload [REST API](https://payloadcms.com/docs/beta/rest-api/overview) under the hood.

### How does it work?

When you need to call the Payload REST API from the server or client, you should use our client, located at `@api`. The client makes all the collection [REST endpoints](https://payloadcms.com/docs/beta/rest-api/overview) exposed by Payload available as functions to call, wrapped into a `fetch` client.

**Important**. Please don't use Payload's [Local API](https://payloadcms.com/docs/beta/rest-api/overview) (for now at least). Although this is a nice feature, we may one day want to decouple our infrastructure, which will be much harder is we're not used the REST API.

When the app starts up, we generate an OpenAPI 3 spec based on the Payload collections (see `generateApi.js`). This spec is then turned in a TypeScript API SDK using `@hey-api/openapi-ts` (see the `generate:api-sdk` npm script). The SDK is then turned into an API client and can be consumed within JS files as named functions.

Examples. For the `Resumes` collection, you have the following endpoints and functions:

| Operation    | Method | Path               | API Function       |
| ------------ | ------ | ------------------ | ------------------ |
| Find         | GET    | /api/resumes       | `findResumes`      |
| Find by ID   | GET    | /api/resumes/{id}  | `findResumeById`   |
| Count        | GET    | /api/resumes/count | `countResumes`     |
| Create       | POST   | /api/resumes       | `createResume`     |
| Update       | PATCH  | /api/resumes       | `updateResumes`    |
| Update By ID | PATCH  | /api/resumes/{id}  | `updateResumeById` |
| Delete       | DELETE | /api/resumes       | `deleteResumes`    |
| Delete By ID | DELETE | /api/resume/{id}   | `deleteResumeById` |

For example, if you wanted to fetch a single resume, you would head to the [Payload REST API docs](https://payloadcms.com/docs/beta/rest-api/overview) and see the Find By ID collection endpoint, then use the client:

```js
import { api } from '@api'

const { data, error } = await api.findResumeById({
  path: { id: 12345 }
})
```

You can dig into the generated API functions code here: `@lib/api-sdk/services.gen.ts`

The api client is `fetch` wrapper, so many of the same options apply, but the shape of the result data is slightly different, which means less `try/catch` (yay) and no need to `.json()`. Some examples:

### Create a resume

```js
import { api } from '@api'

const { data, error } = await api.resumeCreate({
  body: {
    // post body here
  }
})
```

### Update a resume by ID

```js
import { api } from '@api'

const { data, error } = await api.updateResumeById({
  path: { id: '1234' }, // `path` is for replacing params on the URL path
  body: { // post body }
})
```

### Fetch a resume with a query

```js
import { api } from '@api'

const { data, error } = await api.findResumes({
  query: {
    // query is for the query string
    where: {
      // This is Payload query syntax, see docs
      'user.value.id': user.id
    }
  }
})
```

### More help and examples

If you're stuck whilst using the API client, check out the [@hey-api docs](https://heyapi.dev/openapi-ts/get-started.html) and the examples [here](https://stackblitz.com/edit/hey-api-client-fetch-example?file=src%2FApp.tsx).

## Logging

You can just use all the `console` methods to log throughout the codebase. All server logs will be picked up by Pino. See `next-logger.config.cjs` for more info.

**Important restriction**: Be aware the you can't pass more than two args to a log! Any args after the second will just be left off (annoying tbf!).

## Code style

- Single quotes only
- No semi-colons
- Spaces over tabs
- Comments are fine, but try and make the code self-documenting if possible
- Use a [spell-checker](https://marketplace.visualstudio.com/items?itemName=streetsidesoftware.code-spell-checker) for comments
- TypeScript is fine to use, but not required
- Use `.js` or `.ts`, not `.jsx` or `.tsx`

### Prioritise readability

```js
❌

export const useAuth = () => {
  if (useContext(AuthContext) === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
```

```js
✅

export const useAuth = () => {
  const context = useContext(AuthContext)

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }

  return context
}
```

### Use descriptive variable names

```js
❌

if (u._id === tcid) {
  // something
}

```

```js
✅

const isCreator = user._id === teamCreatorId

if (isCreator) {
  // something
}
```

### Use early returns over `else`

```js
❌

function canAccessPage (user) {
  if (user) {
    if (user.isAdmin) {
      return 'Access granted: Welcome, admin!'
    } else {
      return 'Access denied: Insufficient permissions.'
    }
  } else {
    return 'Access denied: Please log in.'
  }
}
```

```js
✅

const canAccessPage = (user) => {
  if (!user) {
    return 'Access denied: Please log in.'
  }

  if (!user.isAdmin) {
    return 'Access denied: Insufficient permissions.'
  }

  return 'Access granted: Welcome, admin!'
}
```

### Prefer named exports over default as it's more explicit

Exception is when NextJS requires it, for example for `.page.js` and `.layout.js` files.

```js
❌

export default function Button() {}
```

```js
✅

export const Button = () => {}
```

```js
❌

const isDev = () => { ... }
export default isDev

import isDev from 'file.js'
```

```js
✅

export const isDev = () => { ... }

...

import { isDev } from 'file.js'
```

### Prefer aliases over relative paths

See tsconfig for aliases, @ === 'src'

```js
❌

import { isDev } from '../../../utils/checkEnv'
```

```js
✅

import { isDev } from '@utils/checkEnv'
```

### Use pnpm over yarn or npm

```bash
❌

yarn add ...
npm install ...
```

```bash
✅

pnpm add ..
pnpm remove ...
```

### Use descriptive present tense commit messages generally

```
❌

fixed prod
```

```
✅

Passes `disabled` prop to `Button`, fixing prod issue
```

### In code, use camelcase for everything apart from classes and React components (use pascal case)

```js
❌

const getAPIMethod = ...
const GetAPIMethod = ...
const get_api_method = ...

const getID = ...
const GetID = ...
const get_id = ...
```

```js
✅

const getApiMethod = ...
const getId = ...
```

### When naming files, use the following rules:

- `PascalCase` for React components
- `train-case` for collections
- `camelcase` for pretty much all other files

_General code file:_

```
❌

- get-resumes.js
- get_resumes.js
- GetResumes.js
```

```
✅

- getResumes.js
```

_Class or React component:_

```
❌

- sign-in-form.js
- signInForm.js
- sign_in_form.js
```

```
✅

- SignInForm.js
```

## Testing production builds locally

- `pnpm run build`
- Spin up Mongo manually via the Docker container
- Change `DATABASE_URI` in .env to `mongodb://localhost:27017`
- `pnpm start`

## Imgproxy

Instructions to set-up image proxy [here](/docs/imgproxy.md)
