name: swift

services:
  nginx:
    image: nginx:latest
    ports:
      - 80:80
      - 443:443
    restart: always
    volumes:
      - ./nginx/conf/:/etc/nginx/conf.d/:ro
      - ./nginx/certs:/etc/nginx/ssl
    attach: false
    logging:
      driver: 'none'

  mongo:
    image: mongo:latest
    ports:
      - '27017:27017'
    command:
      - --storageEngine=wiredTiger
      - --logpath=/var/log/mongodb/mongod.log
    volumes:
      - data:/data/db
    attach: false
    logging:
      driver: 'none'

volumes:
  data:
